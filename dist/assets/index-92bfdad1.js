import{r as a,a as ws,b as Xe}from"./vendor-280e31ee.js";import{N as ls,u as Ke,a as Ns,L as vs,O as ks,b as Ss,B as As,R as Ps,c as ce}from"./router-208768c5.js";import{C as Cs,X as Ds,A as Rs,I as $s,M as Ts,U as Pe,S as Qe,L as Es,a as Ve,b as Ls,T as be,c as Ne,B as os,d as Ce,e as is,f as _s,E as Us,g as Fs,h as Bs,F as ss,i as He,H as ts,D as xe,j as Ms,k as Is,P as we,l as cs,m as De,R as re,n as Os,o as Hs,p as zs,q as ze,r as Ge,s as Ks,t as Vs,u as ds,K as qs,v as Ws,w as Oe,x as ms,y as Js,z as Ys}from"./icons-9d7a79a3.js";import{R as ve,B as us,C as Re,X as $e,Y as Te,T as ke,a as xs,L as Ee,b as Ze,c as es,P as Gs,d as Xs,e as Qs,S as as,f as Zs}from"./charts-2d8bc326.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))f(l);new MutationObserver(l=>{for(const d of l)if(d.type==="childList")for(const c of d.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&f(c)}).observe(document,{childList:!0,subtree:!0});function t(l){const d={};return l.integrity&&(d.integrity=l.integrity),l.referrerPolicy&&(d.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?d.credentials="include":l.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function f(l){if(l.ep)return;l.ep=!0;const d=t(l);fetch(l.href,d)}})();var hs={exports:{}},qe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var et=a,st=Symbol.for("react.element"),tt=Symbol.for("react.fragment"),at=Object.prototype.hasOwnProperty,rt=et.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,nt={key:!0,ref:!0,__self:!0,__source:!0};function gs(s,n,t){var f,l={},d=null,c=null;t!==void 0&&(d=""+t),n.key!==void 0&&(d=""+n.key),n.ref!==void 0&&(c=n.ref);for(f in n)at.call(n,f)&&!nt.hasOwnProperty(f)&&(l[f]=n[f]);if(s&&s.defaultProps)for(f in n=s.defaultProps,n)l[f]===void 0&&(l[f]=n[f]);return{$$typeof:st,type:s,key:d,ref:c,props:l,_owner:rt.current}}qe.Fragment=tt;qe.jsx=gs;qe.jsxs=gs;hs.exports=qe;var e=hs.exports,ps,rs=ws;ps=rs.createRoot,rs.hydrateRoot;const fs=()=>window.location.origin,ys=async(s,n={})=>{const t=localStorage.getItem("adminToken"),f=fs(),l={headers:{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}}},d={...l,...n,headers:{...l.headers,...n.headers}},c=await fetch(`${f}${s}`,d);if(c.status===401){let b="Authentication failed. Please login again.";try{const $=await c.json();$.message&&$.message.includes("expired")?b="Your session has expired. Please login again.":$.message&&$.message.includes("token")&&(b="Invalid session. Please login again.")}catch{}throw window.dispatchEvent(new CustomEvent("auth-error",{detail:{message:b}})),new Error(b)}return c},bs=a.createContext(),fe=()=>{const s=a.useContext(bs);if(!s)throw new Error("useAuth must be used within an AuthProvider");return s},lt=({children:s})=>{const[n,t]=a.useState(!1),[f,l]=a.useState(null),[d,c]=a.useState(!0),[b,$]=a.useState(null),j=(w="Session expired. Please login again.")=>{console.warn("Auto-logout triggered:",w),window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),t(!1),l(null),window.toast&&window.toast(w,"warning"),window.location.href="/login"};a.useEffect(()=>{const w=localStorage.getItem("adminToken"),h=localStorage.getItem("adminUser"),o=localStorage.getItem("redirectAfterLogin");w&&h&&(t(!0),l(JSON.parse(h))),o&&$(o),c(!1);const y=D=>{const{message:v}=D.detail;j(v)};return window.addEventListener("auth-error",y),()=>{window.removeEventListener("auth-error",y)}},[]);const k={isAuthenticated:n,user:f,login:async(w,h)=>{try{const o=fs();console.log(`[AUTH] ${new Date().toISOString()} - Attempting login to:`,`${o}/api/auth/login`),console.log(`[AUTH] Login attempt for email: ${w?w.substring(0,3)+"***":"undefined"}`);const y=new AbortController,D=setTimeout(()=>y.abort(),3e4),v=await fetch(`${o}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:w,password:h}),signal:y.signal});clearTimeout(D),console.log(`[AUTH] Response status: ${v.status}`),console.log("[AUTH] Response headers:",Object.fromEntries(v.headers.entries()));const I=await v.text();console.log("[AUTH] Response text (first 200 chars):",I.substring(0,200));let T;try{T=JSON.parse(I),console.log("[AUTH] Parsed response data:",{success:T.success,message:T.message})}catch(P){console.error("[AUTH] Failed to parse response as JSON:",P),console.error("[AUTH] Raw response text:",I);let S="Server returned invalid response. Please try again.";return I.includes("Internal Server Error")?S="Server is experiencing issues. Please try again in a few moments.":I.includes("timeout")?S="Request timed out. Please check your connection and try again.":I.includes("Database")?S="Database connection issue. Please try again.":v.status>=500?S="Server error occurred. Please try again later.":v.status===404&&(S="Login service not found. Please contact support."),{success:!1,message:S}}if(v.ok&&T.success){console.log("[AUTH] Login successful, storing user data"),localStorage.setItem("adminToken",T.token),localStorage.setItem("adminUser",JSON.stringify(T.user)),t(!0),l(T.user);const P=localStorage.getItem("redirectAfterLogin");return P?(localStorage.removeItem("redirectAfterLogin"),$(null),{success:!0,redirectTo:P}):{success:!0}}else return console.log(`[AUTH] Login failed: ${T.message||"Unknown error"}`),{success:!1,message:T.message||"Login failed. Please check your credentials."}}catch(o){console.error("[AUTH] Login error:",{name:o.name,message:o.message,stack:o.stack});let y="Login failed. Please try again.";return o.name==="AbortError"?y="Request timed out. Please check your connection and try again.":o.message.includes("fetch")?y="Network error. Please check your connection and try again.":o.message.includes("NetworkError")&&(y="Network connection failed. Please try again."),{success:!1,message:y}}},logout:(w=!1)=>{w&&window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),t(!1),l(null)},autoLogout:j,loading:d,redirectPath:b};return e.jsx(bs.Provider,{value:k,children:s})},ot=({message:s,type:n="info",duration:t=3e3,onClose:f})=>{const[l,d]=a.useState(!0);a.useEffect(()=>{const j=setTimeout(()=>{d(!1),setTimeout(f,300)},t);return()=>clearTimeout(j)},[t,f]);const c={success:Cs,error:Ds,warning:Rs,info:$s},b={success:"bg-green-50 text-green-800 border-green-200",error:"bg-red-50 text-red-800 border-red-200",warning:"bg-yellow-50 text-yellow-800 border-yellow-200",info:"bg-blue-50 text-blue-800 border-blue-200"},$=c[n];return e.jsx("div",{className:`fixed top-4 right-4 z-50 transition-all duration-300 ${l?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"}`,children:e.jsxs("div",{className:`flex items-center p-4 rounded-lg border shadow-lg ${b[n]}`,children:[e.jsx($,{className:"h-5 w-5 mr-3 flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:s})]})})},it=({children:s})=>{const[n,t]=a.useState([]),f=(d,c="info",b=3e3)=>{const $=Date.now();t(j=>[...j,{id:$,message:d,type:c,duration:b}])},l=d=>{t(c=>c.filter(b=>b.id!==d))};return Xe.useEffect(()=>{window.toast=f},[]),e.jsxs(e.Fragment,{children:[s,n.map(d=>e.jsx(ot,{message:d.message,type:d.type,duration:d.duration,onClose:()=>l(d.id)},d.id))]})},ct=({children:s})=>{const{isAuthenticated:n,loading:t}=fe();return t?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):n?s:e.jsx(ls,{to:"/login",replace:!0})},dt=()=>{const[s,n]=a.useState(!1),t=a.useRef(null),{logout:f,user:l}=fe(),d=Ke();a.useEffect(()=>{const j=p=>{t.current&&!t.current.contains(p.target)&&n(!1)};return document.addEventListener("mousedown",j),()=>{document.removeEventListener("mousedown",j)}},[]);const c=()=>{f(),d("/login"),n(!1)},b=j=>{d(j),n(!1)},$=()=>{const j=[{label:"Profile",icon:Pe,onClick:()=>b("/profile")}];return((l==null?void 0:l.role)==="admin"||(l==null?void 0:l.role)==="superadmin")&&j.push({label:"Settings",icon:Qe,onClick:()=>b("/settings")}),j.push({label:"Logout",icon:Es,onClick:c,className:"text-red-600 hover:bg-red-50"}),j};return e.jsxs("div",{className:"relative",ref:t,children:[e.jsx("button",{onClick:()=>n(!s),className:"flex items-center p-2 text-gray-600 hover:bg-[#edf1f7] hover:text-gray-800 rounded-md transition-colors",title:"More options",children:e.jsx(Ts,{className:"h-4 w-4"})}),s&&e.jsx("div",{className:"absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:$().map((j,p)=>{const M=j.icon;return e.jsxs("button",{onClick:j.onClick,className:`w-full flex items-center px-4 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${j.className||"text-gray-700"}`,children:[e.jsx(M,{className:"h-4 w-4 mr-3"}),j.label]},p)})})]})},mt=s=>{let n=0;if(s.length===0)return n.toString();for(let t=0;t<s.length;t++){const f=s.charCodeAt(t);n=(n<<5)-n+f,n=n&n}return Math.abs(n).toString(16)},ut=(s,n=32)=>`https://www.gravatar.com/avatar/${mt(s)}?s=${n}&d=identicon`,xt=()=>{const{user:s}=fe(),n=Ns(),[t,f]=a.useState(!1),l=()=>{const d=[{name:"Dashboard",path:"/dashboard",icon:Ls},{name:"Plugin Rank",path:"/plugin-rank",icon:be},{name:"Keyword Analysis",path:"/keyword-analysis",icon:Ne},{name:"Plugin Data Analysis",path:"/analytics",icon:os}];return((s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="superadmin")&&d.push({name:"Team Members",path:"/users",icon:Ce}),d};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[t&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>f(!1)}),e.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${t?"translate-x-0":"-translate-x-full"}`,children:[e.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b",children:[e.jsx("img",{src:"/wpdev.png",className:"",alt:"WPDeveloper Logo"}),e.jsx("button",{onClick:()=>f(!1),className:"lg:hidden p-2 rounded-md hover:bg-gray-100",children:e.jsx(Ve,{className:"h-5 w-5"})})]}),e.jsx("nav",{className:"mt-6",children:l().map(d=>{const c=d.icon,b=n.pathname===d.path;return e.jsxs(vs,{to:d.path,onClick:()=>f(!1),className:`flex items-center px-6 py-3 text-sm font-medium transition-colors ${b?"bg-blue-50 text-blue-600 border-r-2 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[e.jsx(c,{className:"h-5 w-5 mr-3"}),d.name]},d.path)})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full overflow-hidden",children:e.jsx("img",{src:(s==null?void 0:s.profileImage)||ut((s==null?void 0:s.email)||""),alt:"Profile",className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:(s==null?void 0:s.name)||"Admin User"}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:(s==null?void 0:s.role)||"member"})]})]}),e.jsx(dt,{})]})})]}),e.jsx("div",{className:"lg:pl-64",children:e.jsx("main",{className:"p-6",children:e.jsx(ks,{})})})]})},ht=()=>{const[s,n]=a.useState(""),[t,f]=a.useState(""),[l,d]=a.useState(!1),[c,b]=a.useState(!1),[$,j]=a.useState(""),{login:p}=fe(),M=Ke(),k=async w=>{w.preventDefault(),j(""),b(!0);const h=await p(s,t);if(h.success){const o=h.redirectTo||"/dashboard";M(o)}else j(h.message||"Login failed");b(!1)};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-xl p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center mb-4",children:e.jsx("img",{src:"/wpdev_logo.jpeg",className:"h-16 w-16 rounded-full border"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome Back"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your admin account"})]}),e.jsxs("form",{onSubmit:k,className:"space-y-6",children:[$&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:$}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx(is,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"email",type:"email",value:s,onChange:w=>n(w.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(_s,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"password",type:l?"text":"password",value:t,onChange:w=>f(w.target.value),className:"block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0}),e.jsx("button",{type:"button",onClick:()=>d(!l),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:l?e.jsx(Us,{className:"h-5 w-5"}):e.jsx(Fs,{className:"h-5 w-5"})})]})]}),e.jsx("button",{type:"submit",disabled:c,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:c?"Signing in...":"Sign In"})]})]})})})},ge=({isOpen:s,onClose:n,title:t,children:f,maxWidth:l="max-w-xl",fixedHeight:d=!1})=>(a.useEffect(()=>{const c=b=>{b.key==="Escape"&&n()};return s&&(document.addEventListener("keydown",c),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",c),document.body.style.overflow="unset"}},[s,n]),s?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:n}),e.jsxs("div",{className:`relative bg-white rounded-lg shadow-xl ${l} w-full mx-4 transform transition-all ${d?"h-[90vh] flex flex-col":""}`,children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:t}),e.jsx("button",{onClick:n,className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(Ve,{className:"h-5 w-5 text-gray-500"})})]}),e.jsx("div",{className:`p-6 ${d?"flex-1 overflow-y-auto":""}`,children:f})]})]})}):null),gt=({isOpen:s,onClose:n,plugin:t})=>{const f=Ke(),l=a.useRef(null),[d,c]=a.useState([]),[b,$]=a.useState([]),[j,p]=a.useState(0),[M,k]=a.useState(0),[w,h]=a.useState([]),[o,y]=a.useState({}),[D,v]=a.useState(null),[I,T]=a.useState({}),[P,S]=a.useState(""),[u,x]=a.useState(""),[N,R]=a.useState(!1),[_,U]=a.useState(!0),[H,z]=a.useState(!0),[V,G]=a.useState(!0),[X,se]=a.useState(!0),[oe,de]=a.useState(0),ne=(m,B)=>{de(B)},pe=async()=>{if(t)try{U(!0);const m=localStorage.getItem("adminToken"),A=await fetch(`https://pluginsight.vercel.app/api/analytics/download-data/${t.slug}?days=15`,{headers:{Authorization:`Bearer ${m}`}});if(!A.ok)throw new Error("Failed to fetch download data");const O=await A.json();if(O.success&&O.downloadData){const ee=O.downloadData.map(Z=>({date:new Date(Z.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:Z.downloads,fullDate:Z.date}));c(ee)}else c([])}catch(m){console.error("Error fetching download data:",m),c([])}finally{U(!1)}},me=async()=>{if(t)try{z(!0);const m=localStorage.getItem("adminToken"),A=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-info/${t.slug}`,{headers:{Authorization:`Bearer ${m}`}});if(!A.ok)throw new Error("Failed to fetch plugin information");const O=await A.json();O.success&&O.ratings?($(O.ratings),p(O.totalRatings),k(O.averageRating||0)):($([]),p(0),k(0))}catch(m){console.error("Error fetching ratings data:",m),$([]),p(0),k(0)}finally{z(!1)}},g=async()=>{if(t)try{G(!0);const m=localStorage.getItem("adminToken"),A=await fetch(`https://pluginsight.vercel.app/api/analytics/rank-history/${t.slug}?days=15`,{headers:{Authorization:`Bearer ${m}`}});if(!A.ok)throw new Error("Failed to fetch rank history");const O=await A.json();if(O.success&&O.rankHistory){const ee=O.rankHistory.map(Z=>({date:Z.date,rank:Z.rank,fetchedAt:Z.fetchedAt}));h(ee)}else h([])}catch(m){console.error("Error fetching rank history:",m),h([])}finally{G(!1)}},F=async()=>{if(t)try{se(!0);const m=localStorage.getItem("adminToken"),A=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-versions/${t.slug}`,{headers:{Authorization:`Bearer ${m}`}});if(!A.ok)throw new Error("Failed to fetch plugin versions");const O=await A.json();O.success?(y(O.versions||{}),v(O.currentVersion),T(O.oldVersions||{})):(y({}),v(null),T({}))}catch(m){console.error("Error fetching versions data:",m),y({}),v(null),T({})}finally{se(!1)}};a.useEffect(()=>{s&&t&&(pe(),me(),g(),F())},[s,t]),a.useEffect(()=>{const m=B=>{l.current&&!l.current.contains(B.target)&&(R(!1),x(""))};if(N)return document.addEventListener("mousedown",m),()=>{document.removeEventListener("mousedown",m)}},[N]);const L=m=>{var B;return m?((B=m.split(/[-–:]|&#8211;/)[0])==null?void 0:B.trim())||m:""},E=()=>!I||Object.keys(I).length===0?[]:Object.keys(I).filter(m=>m.toLowerCase()==="trunk"||m===D?!1:u.trim()?m.toLowerCase().includes(u.toLowerCase()):!0).sort((m,B)=>{const A=Z=>Z.split(".").map(ae=>parseInt(ae)||0),O=A(m),ee=A(B);for(let Z=0;Z<Math.max(O.length,ee.length);Z++){const ae=(ee[Z]||0)-(O[Z]||0);if(ae!==0)return ae}return 0}),Y=m=>{S(m),R(!1),x("")},i=()=>{R(!N),N||x("")},C=["#10B981","#3B82F6","#F59E0B","#EF4444","#8B5CF6"],Q=m=>{const B=Math.PI/180,{cx:A,cy:O,midAngle:ee,innerRadius:Z,outerRadius:ae,startAngle:Se,endAngle:Le,fill:ie,payload:_e,percent:We,value:Je}=m,Ue=Math.sin(-B*ee),ue=Math.cos(-B*ee),Fe=A+(ae+10)*ue,Ye=O+(ae+10)*Ue,Be=A+(ae+30)*ue,Me=O+(ae+30)*Ue,je=Be+(ue>=0?1:-1)*22,ye=Me,r=ue>=0?"start":"end";return e.jsxs("g",{children:[e.jsxs("text",{x:A,y:O,dy:8,textAnchor:"middle",fill:ie,children:[_e.stars,"★"]}),e.jsx(as,{cx:A,cy:O,innerRadius:Z,outerRadius:ae,startAngle:Se,endAngle:Le,fill:ie}),e.jsx(as,{cx:A,cy:O,startAngle:Se,endAngle:Le,innerRadius:ae+6,outerRadius:ae+10,fill:ie}),e.jsx("path",{d:`M${Fe},${Ye}L${Be},${Me}L${je},${ye}`,stroke:ie,fill:"none"}),e.jsx("circle",{cx:je,cy:ye,r:2,fill:ie,stroke:"none"}),e.jsx("text",{x:je+(ue>=0?1:-1)*12,y:ye,textAnchor:r,fill:"#333",children:`${Je} ratings`}),e.jsx("text",{x:je+(ue>=0?1:-1)*12,y:ye,dy:18,textAnchor:r,fill:"#999",children:`(${(We*100).toFixed(1)}%)`})]})};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(Bs,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[L((t==null?void 0:t.displayName)||(t==null?void 0:t.name))," ","Analytics"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Download trends and rating analysis"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>f(`/plugin-details/${t==null?void 0:t.slug}`),className:"flex items-center space-x-2 px-3 py-2 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded-lg transition-colors",title:"View Plugin Details",children:[e.jsx(ss,{className:"h-4 w-4"}),e.jsx("span",{children:"Plugin Details"})]}),e.jsx("button",{onClick:n,className:"text-gray-400 hover:text-gray-600 transition-colors p-2",children:e.jsx(Ve,{className:"h-6 w-6"})})]})]}),e.jsxs("div",{className:"flex-1 p-6 space-y-4 overflow-y-auto",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(be,{className:"h-5 w-5 mr-2 text-blue-600"}),"Download Trends (Last 15 Days)"]}),_?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):d.length>0?e.jsx(ve,{width:"100%",height:300,children:e.jsxs(us,{data:d,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date"}),e.jsx(Te,{}),e.jsx(ke,{formatter:m=>[m.toLocaleString(),"Downloads"],labelFormatter:m=>`Date: ${m}`}),e.jsx(xs,{dataKey:"downloads",fill:"#3B82F6",children:e.jsx(Ee,{dataKey:"downloads",position:"top",fontSize:10,formatter:m=>m.toLocaleString()})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No download data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(be,{className:"h-5 w-5 mr-2 text-purple-600"}),"15-Day Rank Change"]}),V?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"})}):w.length>0?e.jsx(ve,{width:"100%",height:300,children:e.jsxs(Ze,{data:w,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date"}),e.jsx(Te,{domain:["dataMin - 10","dataMax + 10"],reversed:!0,tickFormatter:m=>`#${m}`}),e.jsx(ke,{formatter:m=>[`#${m}`,"Rank"],labelFormatter:m=>`Date: ${m}`}),e.jsx(es,{type:"monotone",dataKey:"rank",stroke:w.length>1&&w[w.length-1].rank<w[0].rank?"#10B981":"#EF4444",strokeWidth:2,dot:{fill:w.length>1&&w[w.length-1].rank<w[0].rank?"#10B981":"#EF4444",strokeWidth:2,r:4},activeDot:{r:6,strokeWidth:2},children:e.jsx(Ee,{dataKey:"rank",position:"top",formatter:m=>`#${m}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rank history data available"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-gray-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(He,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Rating Distribution"]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-lg font-bold text-gray-900 flex items-center",children:[M?(M/20).toFixed(1):"N/A"," ⭐"]})})]}),H?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"})}):b.length>0?e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 flex-1",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(ve,{width:200,height:200,children:e.jsxs(Gs,{children:[e.jsx(Xs,{activeIndex:oe,activeShape:Q,data:[...b].sort((m,B)=>B.stars-m.stars),cx:"50%",cy:"50%",innerRadius:40,outerRadius:60,fill:"#8884d8",dataKey:"value",onMouseEnter:ne,children:b.map((m,B)=>e.jsx(Qs,{fill:C[B%C.length]},`cell-${B}`))}),e.jsx(ke,{formatter:(m,B,A)=>[`${m} ratings`,`${A.payload.stars} Star${A.payload.stars!==1?"s":""}`]})]})})})}),e.jsxs("div",{className:"bg-white rounded-lg p-4 space-y-2 flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Breakdown"}),e.jsx("div",{className:"space-y-2",children:[...b].sort((m,B)=>B.stars-m.stars).map((m,B)=>{const A=b.length>0?m.value/b.reduce((O,ee)=>O+ee.value,0)*100:0;return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex items-center space-x-1 w-12",children:[e.jsx("span",{className:"text-xs font-medium text-gray-700",children:m.stars}),e.jsx(He,{className:"h-3 w-3 text-yellow-400 fill-current"})]}),e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-500",style:{width:`${A}%`,backgroundColor:C[B%C.length]}})}),e.jsx("div",{className:"text-xs font-medium text-gray-900 w-8 text-right",children:m.value})]},m.stars)})}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-2",children:[e.jsx("div",{className:"text-xs font-bold text-gray-500",children:"Total"}),e.jsx("div",{className:"text-xs font-medium text-gray-900",children:j})]})]})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rating data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(ss,{className:"h-5 w-5 mr-2 text-blue-600"}),"Plugin Downloads"]}),X?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):Object.keys(o).length>0?e.jsxs("div",{className:"space-y-4",children:[D&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(ts,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"font-semibold text-gray-900",children:["Version ",D]}),e.jsx("span",{className:"bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full",children:"Current"})]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Latest stable release"})]})]}),e.jsx("a",{href:o[D],target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center justify-center transition-colors",title:"Download Current Version",children:e.jsx(xe,{className:"h-4 w-4"})})]})}),Object.keys(I).length>0&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(ts,{className:"h-5 w-5 text-gray-600"})}),e.jsx("span",{className:"font-semibold text-gray-900",children:"Previous Versions"}),e.jsx("span",{className:"bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full",children:"Archive"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",ref:l,children:[e.jsxs("button",{type:"button",onClick:i,className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left flex items-center justify-between",children:[e.jsx("span",{className:P?"text-gray-900":"text-gray-500",children:P||"Select a version"}),N?e.jsx(Ms,{className:"h-4 w-4 text-gray-400"}):e.jsx(Is,{className:"h-4 w-4 text-gray-400"})]}),N&&e.jsxs("div",{className:"absolute z-10 w-full bottom-full mb-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:[e.jsx("div",{className:"p-2 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(Ne,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search versions...",value:u,onChange:m=>x(m.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent",autoFocus:!0})]})}),e.jsx("div",{className:"max-h-48 overflow-y-auto",children:E().length>0?E().map(m=>e.jsx("button",{type:"button",onClick:()=>Y(m),className:"w-full px-3 py-2 text-sm text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors",children:m},m)):e.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:u.trim()?"No versions found":"No versions available"})})]})]}),e.jsx("div",{children:e.jsx("a",{href:P?I[P]:"#",target:P?"_blank":"_self",rel:"noopener noreferrer",className:`w-10 h-10 rounded-lg flex items-center justify-center transition-colors ml-3 ${P?"bg-gray-600 hover:bg-gray-700 text-white cursor-pointer":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,title:P?"Download Selected Version":"Select a version first",onClick:P?void 0:m=>m.preventDefault(),children:e.jsx(xe,{className:"h-4 w-4"})})})]})]})})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No version data available"})]})]})]})]})}):null},pt=s=>{if(!s||typeof s!="string")return!1;const n=s.split(".");if(n.length!==3)return!1;try{return n.forEach(t=>{if(t.length===0)throw new Error("Empty JWT part");atob(t.replace(/-/g,"+").replace(/_/g,"/"))}),!0}catch{return!1}},Ae=()=>{const s=localStorage.getItem("adminToken");return s?pt(s)?s:(console.warn("Invalid JWT token found, clearing localStorage"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),null):null},ns=(s,n)=>{var t;((n==null?void 0:n.status)===401||(t=s==null?void 0:s.message)!=null&&t.includes("token"))&&(console.warn("Authentication error detected, clearing tokens"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.pathname!=="/login"&&(window.location.href="/login"))},ft=({plugin:s,onRemove:n,onRefresh:t,canAddPlugins:f})=>{var p,M;const[l,d]=a.useState(!1),[c,b]=a.useState(!1),$=k=>{if(!k)return{formatted:"N/A",daysDiff:"N/A"};try{const w=k.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!w)return{formatted:"N/A",daysDiff:"N/A"};const[,h,o,y]=w,D=`${y}-${o}-${h}`,v=new Date(`${h}-${o}-${y}`),I=new Date;if(isNaN(v.getTime()))return{formatted:"N/A",daysDiff:"N/A"};const T=I-v,P=Math.floor(T/(1e3*60*60*24));return{formatted:D,daysDiff:P}}catch{return{formatted:"N/A",daysDiff:"N/A"}}},j=async()=>{if(t){b(!0);try{await t(s.slug)}finally{b(!1)}}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2 flex-1 overflow-hidden",children:[e.jsxs("div",{className:"w-12 h-12 border rounded-lg flex items-center justify-center overflow-hidden",children:[s.icons&&(s.icons["2x"]||s.icons["1x"])?e.jsx("img",{src:s.icons["2x"]||s.icons["1x"],alt:`${s.displayName} icon`,className:"w-full h-full object-cover rounded-lg",onError:k=>{k.target.style.display="none",k.target.nextSibling.style.display="flex"}}):null,e.jsx(cs,{className:`h-6 w-6 text-black ${s.icons&&(s.icons["2x"]||s.icons["1x"])?"hidden":""}`})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-semibold text-gray-900 truncate text-lg",children:s.displayName}),e.jsx("p",{className:"text-sm text-gray-500 font-mono whitespace-nowrap",children:s.slug})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("button",{onClick:j,disabled:c,className:"text-gray-400 hover:text-green-500 transition-colors p-1 disabled:opacity-50",title:"Refresh plugin data",children:e.jsx(re,{className:`h-4 w-4 ${c?"animate-spin":""}`})}),f&&e.jsx("button",{onClick:()=>n(s.slug),className:"text-gray-400 hover:text-red-500 transition-colors p-1",title:"Remove plugin",children:e.jsx(De,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between space-x-4",children:[e.jsxs("span",{className:"text-sm font-medium px-2 py-1 rounded-full bg-green-100 text-green-800",children:["v",s.version||"N/A"]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",s.currentRank||"N/A"]}),((p=s.rankHistory)==null?void 0:p.rankChange)!==null&&((M=s.rankHistory)==null?void 0:M.rankChange)!==void 0&&e.jsxs("span",{className:`text-xs ${s.rankHistory.rankChange>0?"text-green-600":s.rankHistory.rankChange<0?"text-red-600":"text-gray-600"}`,children:[s.rankHistory.rankChange>0?"↑":s.rankHistory.rankChange<0?"↓":"→",Math.abs(s.rankHistory.rankChange)]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Released"}),e.jsx("div",{className:`text-sm font-medium px-2 py-1 rounded ${(()=>{const k=s.lastReleaseDate||s.lastFetched,w=$(k);return w.daysDiff==="N/A"||w.daysDiff<=20?"bg-gray-100 text-gray-700":"bg-yellow-50 text-yellow-700"})()}`,children:(()=>{const k=s.lastReleaseDate||s.lastFetched,w=$(k);return e.jsxs(e.Fragment,{children:[w.formatted,w.daysDiff!=="N/A"&&e.jsxs("span",{className:"text-xs ml-1",children:["(",w.daysDiff," days)"]})]})})()})]})]}),e.jsxs("div",{className:"mt-4 border border-gray-200 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 px-3 py-2 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:"Download Trends"}),s.downloadTrend&&e.jsxs("span",{className:`text-xs px-2 py-1 rounded-full ${s.downloadTrend.isPositive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[s.downloadTrend.isPositive?"↑":"↓"," ",s.downloadTrend.changePercent,"%"]})]})}),s.downloadTrend?e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxs("tbody",{className:"bg-white divide-y divide-gray-200",children:[e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Yesterday"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-700 font-bold text-right",children:s.downloadTrend.yesterdayDownloads.toLocaleString()})]}),e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Day Before"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-900 font-bold text-right",children:s.downloadTrend.dayBeforeDownloads.toLocaleString()})]})]}),e.jsx("tfoot",{className:"bg-gray-100 border-t border-gray-200",children:e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-2 text-sm font-semibold text-gray-900",children:"Changes"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-right",children:e.jsxs("span",{className:`text-sm font-bold ${s.downloadTrend.change>=0?"text-green-600":"text-red-600"}`,children:[s.downloadTrend.change>=0?"+":"",s.downloadTrend.change.toLocaleString()]})})]})})]}):e.jsx("div",{className:"text-center py-4",children:e.jsx("span",{className:"text-xs text-gray-500",children:"No download trend data available"})})]}),e.jsx("div",{className:"pt-4 border-t border-gray-100",children:e.jsxs("button",{onClick:()=>d(!0),className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-2",children:[e.jsx(Os,{className:"h-4 w-4"}),e.jsx("span",{children:"View Chart & Analytics"})]})})]}),e.jsx(gt,{isOpen:l,onClose:()=>d(!1),plugin:s})]})},yt=()=>{var de,ne,pe,me;const{user:s,autoLogout:n}=fe(),[t,f]=a.useState(!1),[l,d]=a.useState(""),[c,b]=a.useState(!1),[$,j]=a.useState(!1),[p,M]=a.useState(null),[k,w]=a.useState([]),[h,o]=a.useState(null),[y,D]=a.useState(!1),[v,I]=a.useState(!1),[T,P]=a.useState(null),[S,u]=a.useState(!1),x=s&&["admin","superadmin"].includes(s.role),N=async()=>{try{const g=Ae();if(!g){console.warn("No valid authentication token found for database check"),n("No valid authentication token found. Please login again.");return}const L=await fetch("https://pluginsight.vercel.app/api/plugins/check-database",{headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"}});if(L.ok){const E=await L.json();E.success&&j(E.hasPlugins)}else if(L.status===401){console.warn("Authentication failed during database check");let E="Your session has expired. Please login again.";try{const Y=await L.json();Y.message&&(E=Y.message)}catch{}n(E)}else ns(null,L)}catch(g){if(console.error("Error checking database status:",g),g.message&&(g.message.includes("expired")||g.message.includes("token")||g.message.includes("Authentication failed"))){console.warn("JWT token error detected during database check:",g.message),n(g.message);return}ns(g)}},R=async()=>{try{b(!0),M({current:0,total:0,page:0,totalPages:0,successCount:0,errorCount:0,percentComplete:0,estimatedTimeRemaining:null,averageTimePerPage:null,pluginsPerSecond:0,message:"Starting full plugin fetch (all 55,540+ plugins)..."});const g=Ae();if(!g){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const L=await fetch("https://pluginsight.vercel.app/api/plugins/fetch-all",{method:"POST",headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"}});if(!L.ok){if(L.status===401){console.warn("Authentication failed during plugin fetch");let i="Your session has expired. Please login again.";try{const C=await L.json();C.message&&(i=C.message)}catch{}n(i);return}throw new Error(`HTTP error! status: ${L.status}`)}const E=L.body.getReader(),Y=new TextDecoder;for(;;){const{done:i,value:C}=await E.read();if(i)break;const m=Y.decode(C).split(`
`).filter(B=>B.trim());for(const B of m)try{const A=JSON.parse(B);if(A.type==="progress")M({current:A.current||0,total:A.total||0,page:A.page||0,totalPages:A.totalPages||0,successCount:A.successCount||0,errorCount:A.errorCount||0,percentComplete:A.percentComplete||0,estimatedTimeRemaining:A.estimatedTimeRemaining,averageTimePerPage:A.averageTimePerPage,pluginsPerSecond:A.pluginsPerSecond||0,message:A.message||"Processing..."});else if(A.type==="complete")M({current:A.summary.totalProcessedPlugins,total:A.summary.totalPlugins,page:A.summary.totalPages,totalPages:A.summary.totalPages,successCount:A.summary.successfulPages,errorCount:A.summary.failedPages,percentComplete:100,averageTimePerPage:A.summary.averageTimePerPage,pluginsPerSecond:A.summary.averagePluginsPerSecond,successRate:A.summary.successRate,totalDuration:A.summary.totalDuration,message:`✅ Fetch completed! ${A.summary.totalProcessedPlugins.toLocaleString()} plugins processed in ${Math.round(A.summary.totalDuration/1e3/60)} minutes`}),A.errors&&A.errors.length>0&&console.warn("Some errors occurred during fetch:",A.errors),window.toast(`Successfully fetched ${A.summary.totalProcessedPlugins.toLocaleString()} plugins!`,"success");else if(A.type==="error")throw new Error(A.message||"Fetch failed")}catch(A){console.warn("Failed to parse streaming data:",A)}}}catch(g){if(console.error("Fetch all plugins error:",g),g.message&&(g.message.includes("expired")||g.message.includes("token")||g.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin fetch:",g.message),n(g.message);return}M({current:0,total:0,message:`❌ Full fetch failed: ${g.message}`,error:!0}),window.toast(`Fetch failed: ${g.message}`,"error")}finally{b(!1),setTimeout(()=>M(null),1e4),N()}},_=async()=>{try{const g=await ys("/api/plugins/added");if(!g.ok){let L=`HTTP error! status: ${g.status}`;try{const E=await g.json();E.message&&(L=E.message)}catch{}throw new Error(L)}const F=await g.json();if(F.success){const L=F.addedPlugins.map(E=>{var Y;return{slug:E.pluginSlug,name:E.pluginName,displayName:E.displayName,currentRank:E.currentRank,rankGrowth:((Y=E.rankHistory)==null?void 0:Y.rankChange)||0,lastFetched:E.lastUpdated,short_description:E.short_description,version:E.version,lastReleaseDate:E.lastReleaseDate,icons:E.icons||{},rating:E.rating,numRatings:E.numRatings||0,currentVersion:E.currentVersion,previousVersions:E.previousVersions||[],rankHistory:E.rankHistory,downloadTrend:E.downloadTrend,downloadDataHistory:E.downloadDataHistory||[],reviewStats:E.reviewStats,versionInfo:E.versionInfo,pluginInformation:E.pluginInformation}});w(L)}else console.warn("Failed to load added plugins:",F.message)}catch(g){console.error("Error loading added plugins:",g),g.name==="TypeError"&&g.message.includes("Failed to fetch")?(console.warn("Unable to connect to server - backend may be down"),w([])):(console.warn("Failed to load added plugins:",g.message),w([]))}},U=g=>{try{localStorage.setItem("pluginData",JSON.stringify(g))}catch(F){console.error("Error storing plugin data in localStorage:",F)}},H=()=>{try{const g=localStorage.getItem("pluginData");return g?JSON.parse(g):null}catch(g){return console.error("Error retrieving plugin data from localStorage:",g),null}},z=()=>{localStorage.removeItem("pluginData")},V=async()=>{if(!l.trim()){window.toast("Please enter a plugin slug","warning");return}try{D(!0);const g=`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${l.trim()}`,F=await fetch(g);if(!F.ok)throw new Error(`WordPress API error: ${F.status}`);const L=await F.json();if(L.error){window.toast(`Plugin not found: ${L.error}`,"error"),o(null);return}U(L),o({slug:L.slug,name:L.name,version:L.version,author:L.author,rating:L.rating,active_installs:L.active_installs,num_ratings:L.num_ratings,downloaded:L.downloaded,last_updated:L.last_updated,homepage:L.homepage,requires:L.requires,tested:L.tested,requires_php:L.requires_php}),window.toast("Plugin data fetched successfully from WordPress API","success")}catch(g){console.error("Error fetching plugin data:",g),window.toast("Failed to fetch plugin data from WordPress API","error"),o(null)}finally{D(!1)}};a.useEffect(()=>{_(),N()},[]);const G=async()=>{const g=H();if(!g){window.toast("Please fetch plugin data first by clicking the Fetch button","warning");return}if(!l.trim()){window.toast("Please enter a plugin slug","warning");return}u(!0);try{const F=Ae();if(!F){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const E=await fetch("https://pluginsight.vercel.app/api/plugins/added-with-data",{method:"POST",headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"},body:JSON.stringify({slug:l.trim(),pluginData:g})});if(!E.ok&&E.status===401){console.warn("Authentication failed during plugin addition");let i="Your session has expired. Please login again.";try{const C=await E.json();C.message&&(i=C.message)}catch{}n(i);return}const Y=await E.json();Y.success?(window.toast(Y.message,"success"),z(),d(""),f(!1),o(null),await _()):window.toast(Y.message||"Failed to add plugin","error")}catch(F){if(console.error("Add plugin error:",F),F.message&&(F.message.includes("expired")||F.message.includes("token")||F.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin addition:",F.message),n(F.message);return}window.toast("Failed to add plugin. Please try again.","error")}finally{u(!1)}},X=g=>{const F=k.find(L=>L.slug===g)||addedPluginsListData.find(L=>L.slug===g);P(F),I(!0)},se=async()=>{if(T)try{const g=Ae();if(!g){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const L=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${T.slug}`,{method:"DELETE",headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"}});if(!L.ok&&L.status===401){console.warn("Authentication failed during plugin removal");let Y="Your session has expired. Please login again.";try{const i=await L.json();i.message&&(Y=i.message)}catch{}n(Y);return}const E=await L.json();E.success?(window.toast("Plugin removed successfully","success"),await _()):window.toast(E.message||"Failed to remove plugin","error")}catch(g){if(console.error("Remove plugin error:",g),g.message&&(g.message.includes("expired")||g.message.includes("token")||g.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin removal:",g.message),n(g.message);return}window.toast("Failed to remove plugin","error")}finally{I(!1),P(null)}},oe=async g=>{try{const F=Ae();if(!F){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const E=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${g}/refresh`,{method:"POST",headers:{Authorization:`Bearer ${F}`,"Content-Type":"application/json"}});if(!E.ok&&E.status===401){console.warn("Authentication failed during plugin refresh");let i="Your session has expired. Please login again.";try{const C=await E.json();C.message&&(i=C.message)}catch{}n(i);return}const Y=await E.json();Y.success?(window.toast(Y.message,"success"),await _()):window.toast(Y.message||"Failed to refresh plugin","error")}catch(F){if(console.error("Refresh plugin error:",F),F.message&&(F.message.includes("expired")||F.message.includes("token")||F.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin refresh:",F.message),n(F.message);return}window.toast("Failed to refresh plugin","error")}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold text-gray-900",children:"Welcome to Admin Dashboard"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your plugins, analyze keywords, and track performance all in one place."})]}),e.jsxs("div",{className:"flex gap-4",children:[x&&e.jsx("button",{onClick:R,disabled:c,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${c?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white`,title:"Fetch all 55,540+ plugins from WordPress repository",children:c?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Fetching All..."]}):e.jsxs(e.Fragment,{children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),$?"Refetch":"Fetch"]})}),x&&e.jsxs("button",{onClick:()=>f(!0),disabled:c,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${c?"bg-gray-400 cursor-not-allowed":"bg-green-600 hover:bg-green-700"} text-white`,children:[e.jsx(we,{className:"h-4 w-4 mr-2"}),"Add Plugin"]})]})]}),p&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg border ${p.error?"bg-red-50 border-red-200":"bg-blue-50 border-blue-200"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:`text-sm font-medium ${p.error?"text-red-900":"text-blue-900"}`,children:p.message}),p.total>0&&e.jsxs("span",{className:`text-sm ${p.error?"text-red-700":"text-blue-700"}`,children:[(de=p.current)==null?void 0:de.toLocaleString(),"/",(ne=p.total)==null?void 0:ne.toLocaleString()]})]}),p.page&&p.totalPages&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-700",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["Page: ",p.page,"/",p.totalPages]}),e.jsxs("div",{children:["Progress: ",p.percentComplete||0,"%"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["✅ ",(pe=p.successCount)==null?void 0:pe.toLocaleString()," success"]}),p.errorCount>0&&e.jsxs("div",{className:"text-red-600",children:["❌ ",(me=p.errorCount)==null?void 0:me.toLocaleString()," errors"]})]})]}),(p.pluginsPerSecond>0||p.estimatedTimeRemaining)&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-600",children:[p.pluginsPerSecond>0&&e.jsxs("div",{children:["Speed: ",p.pluginsPerSecond," plugins/sec"]}),p.estimatedTimeRemaining&&e.jsxs("div",{children:["ETA:"," ",Math.round(p.estimatedTimeRemaining/1e3/60)," ","min"]}),p.averageTimePerPage&&e.jsxs("div",{children:["Avg: ",Math.round(p.averageTimePerPage/1e3),"s/page"]}),p.successRate&&e.jsxs("div",{children:["Success Rate: ",p.successRate,"%"]})]}),p.totalDuration&&e.jsx("div",{className:"mb-3 text-xs text-green-700 bg-green-50 p-2 rounded",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:["Duration:"," ",Math.round(p.totalDuration/1e3/60)," ","minutes"]}),e.jsxs("div",{children:["Avg Speed: ",p.pluginsPerSecond," plugins/sec"]}),e.jsxs("div",{children:["Success Rate: ",p.successRate,"%"]}),e.jsxs("div",{children:["Pages: ",p.successCount,"/",p.totalPages]})]})}),p.total>0&&!p.error&&e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-3",children:e.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-300 flex items-center justify-center",style:{width:`${Math.max(2,p.percentComplete||p.current/p.total*100)}%`},children:e.jsxs("span",{className:"text-xs text-white font-medium",children:[p.percentComplete||Math.round(p.current/p.total*100),"%"]})})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-xl font-semibold text-gray-900",children:["Added Plugins (",k.length,")"]}),e.jsx("div",{className:"text-sm text-gray-500",children:k.length>0?`Showing ${k.length} plugins`:"No plugins added yet"})]}),k.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:k.map(g=>e.jsx(ft,{plugin:g,onRemove:X,onRefresh:oe,canAddPlugins:x},g.slug))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(cs,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins added yet"}),e.jsx("p",{className:"text-gray-600",children:'Start tracking your WordPress plugins by adding them to your dashboard using the "Add Plugin" button above.'})]})]}),e.jsx(ge,{isOpen:t,onClose:()=>{S||(f(!1),o(null),d(""),z())},title:"Add New Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pluginSlug",className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{id:"pluginSlug",type:"text",value:l,onChange:g=>d(g.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., my-awesome-plugin"}),e.jsx("button",{onClick:V,disabled:y||!l.trim(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:y?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Fetching..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(xe,{className:"h-4 w-4"}),e.jsx("span",{children:"Fetch"})]})})]})]}),h&&e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 space-y-3",children:[e.jsx("h4",{className:"font-semibold text-green-900",children:"Plugin Information (Fetched from WordPress API)"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Name:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Version:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.version||"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Author:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:(()=>{const g=h.author;if(!g)return"N/A";const F=g.match(/<a[^>]*>(.*?)<\/a>/);return F?F[1]:g})()})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Rating:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.rating?`${h.rating}/100`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Active Installs:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.active_installs?h.active_installs.toLocaleString():"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Last Updated:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:h.last_updated||"N/A"})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("span",{className:"text-green-700",children:"WordPress Requirements:"}),e.jsxs("span",{className:"ml-2 font-medium text-green-900",children:["WP ",h.requires||"N/A"," | Tested up to"," ",h.tested||"N/A"," | PHP"," ",h.requires_php||"N/A"]})]})]})]}),!h&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("p",{className:"text-blue-800 text-sm",children:[e.jsx("strong",{children:"Step 1:"}),' Enter a plugin slug and click "Fetch" to retrieve plugin information from WordPress API.',e.jsx("br",{}),e.jsx("strong",{children:"Step 2:"}),' Once plugin data is displayed, click "Add Plugin" to add it to your dashboard.']})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{f(!1),o(null),d(""),z()},disabled:S,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),e.jsx("button",{onClick:G,disabled:S||!h,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:S?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Adding..."})]}):e.jsx("span",{children:"Add Plugin"})})]})]})}),e.jsx(ge,{isOpen:v,onClose:()=>{I(!1),P(null)},title:"Confirm Delete",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(De,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Delete Plugin"}),e.jsxs("p",{className:"text-gray-600",children:['Are you sure you want to remove "',(T==null?void 0:T.displayName)||(T==null?void 0:T.name),'" from your added plugins?']})]})]}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm text-yellow-800",children:[e.jsx("strong",{children:"Warning:"})," This action cannot be undone. The plugin will be removed from your dashboard and you'll need to add it again if you want to track it."]})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{I(!1),P(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsxs("button",{onClick:se,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2",children:[e.jsx(De,{className:"h-4 w-4"}),e.jsx("span",{children:"Delete Plugin"})]})]})]})})]})},bt=()=>{var T,P,S;const[s,n]=a.useState([]),[t,f]=a.useState(""),[l,d]=a.useState("7"),[c,b]=a.useState({start:"",end:""}),[$,j]=a.useState([]),[p,M]=a.useState(!1),[k,w]=a.useState(!1);console.log("Chart data: ",$);const h=u=>{var V;const{cx:x,cy:N,payload:R}=u;if(!t||!R)return null;const _=((V=s.find(G=>G.pluginSlug===t))==null?void 0:V.displayName)||t,U=R[`${_}_trend`],H=R[_];if(H==null)return null;let z="#3B82F6";return U==="improvement"?z="#10B981":U==="decline"&&(z="#EF4444"),e.jsx("circle",{cx:x,cy:N,r:5,fill:z,stroke:z,strokeWidth:2})},o=async()=>{var u;try{const x=localStorage.getItem("adminToken"),R=await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${x}`,"Content-Type":"application/json"}});if(!R.ok)throw new Error(`HTTP error! status: ${R.status}`);const _=await R.json();_.success?(n(_.plugins),console.log(`✅ Loaded ${((u=_.plugins)==null?void 0:u.length)||0} plugins from plugininformations collection (${_.pluginsWithRankHistory||0} with rank history)`)):console.warn("Failed to load plugins:",_.message)}catch(x){console.error("Error loading plugins from plugininformations collection:",x),x.name==="TypeError"&&x.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins from database","error")}},y=async()=>{if(!t){j([]);return}try{M(!0);const u=s.find(N=>N.pluginSlug===t);if(!u){console.error("Selected plugin not found in loaded plugins"),window.toast("Selected plugin not found","error"),j([]);return}if(!u.rankHistory||!Array.isArray(u.rankHistory)){console.log(`Plugin ${t} has no rank history data`),j([]);return}console.log(`📊 Processing rank history for ${t}: ${u.rankHistory.length} entries`);const x=D(u);j(x)}catch(u){console.error("Error loading chart data:",u),window.toast("Failed to load chart data","error")}finally{M(!1)}},D=u=>{if(!u.rankHistory||!Array.isArray(u.rankHistory))return[];const x=u.displayName||u.pluginName||u.pluginSlug;let N=u.rankHistory;if(l!=="custom"){const U=parseInt(l),H=new Date;H.setDate(H.getDate()-U),N=u.rankHistory.filter(z=>{const[V,G,X]=z.date.split("-");return new Date(X,G-1,V)>=H})}else if(c.start&&c.end){const U=new Date(c.start),H=new Date(c.end);N=u.rankHistory.filter(z=>{const[V,G,X]=z.date.split("-"),se=new Date(X,G-1,V);return se>=U&&se<=H})}return N.sort((U,H)=>{const[z,V,G]=U.date.split("-"),[X,se,oe]=H.date.split("-"),de=new Date(G,V-1,z),ne=new Date(oe,se-1,X);return de-ne}).map((U,H,z)=>{const V=z[H-1];let G="stable";return V&&(U.previousRank<V.previousRank?G="improvement":U.previousRank>V.previousRank&&(G="decline")),{date:U.date,[x]:U.previousRank,[`${x}_trend`]:G}})},v=async()=>{if(!t){window.toast("Please select a plugin first","warning");return}try{M(!0),window.toast("Refreshing chart data...","info"),await y(),window.toast("Chart data refreshed successfully","success")}catch(u){console.error("Error refreshing chart data:",u),window.toast("Failed to refresh chart data","error")}finally{M(!1)}};a.useEffect(()=>{o()},[]),a.useEffect(()=>{y()},[t,l,c]);const I=u=>{d(u),w(u==="custom")};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(be,{className:"h-8 w-8 text-blue-600 mr-3"}),"Plugin Rank Analysis"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Track ranking trends for your added plugins"})]})})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plugin"}),e.jsxs("select",{value:t,onChange:u=>f(u.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(u=>e.jsx("option",{value:u.pluginSlug,children:u.displayName},u.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:l,onChange:u=>I(u.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom range"})]})]}),k&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:c.start,onChange:u=>b(x=>({...x,start:u.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:c.end,onChange:u=>b(x=>({...x,end:u.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:v,disabled:p||!t,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Fetch latest rank from WordPress and save to database",children:[e.jsx(re,{className:`h-4 w-4 mr-2 ${p?"animate-spin":""}`}),"Refresh"]})})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:["Plugin Rank Trends",t&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["(",((T=s.find(u=>u.pluginSlug===t))==null?void 0:T.displayName)||t,")"]})]}),p?e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(re,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading chart data..."})]})}):$.length>0?e.jsx("div",{className:"h-96",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(Ze,{data:$,children:[e.jsx(Re,{strokeDasharray:"3 3"}),e.jsx($e,{dataKey:"date",tick:{fontSize:12},angle:-45,textAnchor:"end",height:60}),e.jsx(Te,{tick:{fontSize:12},domain:(()=>{var G;if($.length===0)return[1,100];const u=((G=s.find(X=>X.pluginSlug===t))==null?void 0:G.displayName)||t,x=$.map(X=>X[u]).filter(X=>X!=null);if(x.length===0)return[1,100];const N=Math.min(...x),R=Math.max(...x),_=x[x.length-1],U=Math.max(1,_-10),H=_+10,z=Math.min(U,N-2),V=Math.max(H,R+2);return[z,V]})(),reversed:!0,label:{value:"Rank",angle:-90,position:"insideLeft"},allowDecimals:!1,type:"number"}),e.jsx(ke,{labelFormatter:u=>`Date: ${u}`,formatter:(u,x)=>[u?`#${u}`:"No data",x]}),e.jsx(Zs,{}),t&&e.jsx(es,{type:"monotone",dataKey:((P=s.find(u=>u.pluginSlug===t))==null?void 0:P.displayName)||t,stroke:"#3B82F6",strokeWidth:2,dot:e.jsx(h,{}),connectNulls:!1,activeDot:{r:6,stroke:"#3B82F6",strokeWidth:2},children:e.jsx(Ee,{dataKey:((S=s.find(u=>u.pluginSlug===t))==null?void 0:S.displayName)||t,position:"top",fontSize:10,fill:"#374151",formatter:u=>u?`#${u}`:""})},t)]})})}):e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(be,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Data Available"}),e.jsx("p",{className:"text-gray-600 mb-4",children:t?"No rank history found for the selected plugin and date range":"Select a plugin to view its rank trends"}),s.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Add plugins from the Dashboard first to see their ranking trends."})]})})]})]})},js=s=>{const n=document.createElement("textarea");return n.innerHTML=s,n.value},jt=(s,n=40)=>{const t=js(s);if(t.length<=n)return t;const f=t.split(" ");let l=f[0];for(let d=1;d<f.length&&(l+" "+f[d]).length<=n-3;d++)l+=" "+f[d];return l+"..."},wt=()=>{var ye;const[s,n]=a.useState("performance"),[t,f]=a.useState([]),[l,d]=a.useState(""),[c,b]=a.useState([]),[$,j]=a.useState(!1),[p,M]=a.useState(!1),[k,w]=a.useState(""),[h,o]=a.useState(""),[y,D]=a.useState(!1),[v,I]=a.useState(new Set),[T,P]=a.useState(!1),[S,u]=a.useState(new Set),[x,N]=a.useState(null),[R,_]=a.useState(!1),[U,H]=a.useState([]),[z,V]=a.useState(!1),[G,X]=a.useState(!1),[se,oe]=a.useState(""),[de,ne]=a.useState({}),[pe,me]=a.useState(!1),[g,F]=a.useState(10),[L,E]=a.useState(!1),[Y,i]=a.useState(!1),[C,Q]=a.useState(null),[m,B]=a.useState([]),[A,O]=a.useState([]),[ee,Z]=a.useState(!1),[ae,Se]=a.useState(!1),Le=async()=>{var r;try{const K=localStorage.getItem("adminToken"),J=await(await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${K}`,"Content-Type":"application/json"}})).json();J.success?(f(J.plugins),console.log(`✅ Loaded ${((r=J.plugins)==null?void 0:r.length)||0} plugins from plugininformations collection for keyword analysis`)):console.error("Failed to load plugins:",J.message)}catch(K){console.error("Error loading plugins from plugininformations collection:",K),window.toast("Failed to load plugins from database","error")}},ie=async()=>{try{if(j(!0),!l){b([]),ne({}),j(!1);return}const r=localStorage.getItem("adminToken"),W=`https://pluginsight.vercel.app/api/keywords?pluginSlug=${l}`,J=await(await fetch(W,{headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}})).json();if(J.success){const le=J.keywords.map(he=>({...he,position:he.latestRank,lastChecked:he.lastChecked||he.updatedAt}));b(le);const te={};le.forEach(he=>{te[he._id]=he.occurrences||0}),ne(te),F(10)}else console.error("Failed to load keywords:",J.message),window.toast(J.message||"Failed to load keywords","error"),b([]),ne({})}catch(r){console.error("Error loading keywords:",r),window.toast("Failed to load keywords","error"),b([]),ne({})}finally{j(!1)}},_e=async()=>{if(!h||!k.trim()){window.toast("Please select a plugin and enter a keyword","error");return}try{const r=localStorage.getItem("adminToken"),K=t.find(le=>le.pluginSlug===h),J=await(await fetch("https://pluginsight.vercel.app/api/keywords",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:h,pluginName:(K==null?void 0:K.displayName)||h,keyword:k.trim()})})).json();J.success?(window.toast("Keyword added successfully","success"),w(""),o(""),M(!1),h===l&&ie()):window.toast(J.message||"Failed to add keyword","error")}catch(r){console.error("Error adding keyword:",r),window.toast("Failed to add keyword","error")}},We=async()=>{try{D(!0),window.toast("Refreshing keyword ranks...","info");const r=localStorage.getItem("adminToken"),q=await(await fetch("https://pluginsight.vercel.app/api/keywords/refresh-ranks",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}})).json();q.success?(window.toast(q.message,"success"),await ie()):window.toast(q.message||"Failed to refresh keyword ranks","error")}catch(r){console.error("Error refreshing keyword ranks:",r),window.toast("Failed to refresh keyword ranks","error")}finally{D(!1)}},Je=async()=>{try{const r=localStorage.getItem("adminToken"),K="https://pluginsight.vercel.app",W=Array.from(v),J=await(await fetch(`${K}/api/keywords/bulk-delete`,{method:"DELETE",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({keywordIds:W})})).json();J.success?(window.toast(`${W.length} keywords deleted successfully`,"success"),I(new Set),P(!1),ie()):window.toast(J.message||"Failed to delete keywords","error")}catch(r){console.error("Error deleting keywords:",r),window.toast("Failed to delete keywords","error")}},Ue=async()=>{if(x)try{const r=localStorage.getItem("adminToken"),q=await(await fetch(`https://pluginsight.vercel.app/api/keywords/${x}`,{method:"DELETE",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}})).json();if(q.success){window.toast("Keyword deleted successfully","success"),_(!1),N(null);const J=new Set(S);J.delete(x),u(J),ie()}else window.toast(q.message||"Failed to delete keyword","error")}catch(r){console.error("Error deleting keyword:",r),window.toast("Failed to delete keyword","error")}},ue=async(r=!1)=>{try{V(!0);const K=localStorage.getItem("adminToken"),le=await(await fetch(`https://pluginsight.vercel.app/api/competitors${r?"?autoDiscover=true":""}`,{headers:{Authorization:`Bearer ${K}`,"Content-Type":"application/json"}})).json();le.success&&(H(le.competitors),r&&le.competitors.length>0&&window.toast(`Discovered ${le.competitors.length} competitor plugins`,"success"))}catch(K){console.error("Error loading competitors:",K),window.toast("Failed to load competitors","error")}finally{V(!1)}},Fe=async()=>{if(!se.trim()){window.toast("Please enter a plugin slug","error");return}try{const r=localStorage.getItem("adminToken"),q=await(await fetch("https://pluginsight.vercel.app/api/competitors",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:se.trim()})})).json();q.success?(window.toast("Competitor added successfully","success"),oe(""),X(!1),ue()):window.toast(q.message||"Failed to add competitor","error")}catch(r){console.error("Error adding competitor:",r),window.toast("Failed to add competitor","error")}},Ye=async r=>{Q(r),me(!0),Z(!0);try{const K=localStorage.getItem("adminToken"),J=await(await fetch(`https://pluginsight.vercel.app/api/keywords/ranks/${encodeURIComponent(r.keyword)}/${r.pluginSlug}?limit=30`,{headers:{Authorization:`Bearer ${K}`,"Content-Type":"application/json"}})).json();J.success?B(J.rankHistory):(window.toast("Failed to load rank history","error"),B([]))}catch(K){console.error("Error loading rank history:",K),window.toast("Failed to load rank history","error"),B([])}finally{Z(!1)}},Be=async r=>{Q(r),i(!0),Se(!0);try{const W=`https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(r.keyword)}&request[per_page]=50&request[fields][active_installs]=true&request[fields][ratings]=true&request[fields][tested]=true&request[fields][last_updated]=true`,J=await(await fetch(W)).json();if(J&&J.plugins){const le=J.plugins.filter(te=>te.slug!==r.pluginSlug).sort((te,he)=>(he.active_installs||0)-(te.active_installs||0)).slice(0,10).map(te=>({pluginName:te.name,pluginSlug:te.slug,activeInstalls:te.active_installs||0,rating:te.rating||0,numRatings:te.num_ratings||0,testedUpTo:te.tested||"N/A",lastUpdated:te.last_updated||"N/A",wordpressUrl:`https://wordpress.org/plugins/${te.slug}/`}));O(le)}else window.toast("Failed to load related plugins","error"),O([])}catch(K){console.error("Error loading related plugins:",K),window.toast("Failed to load related plugins","error"),O([])}finally{Se(!1)}};a.useEffect(()=>{Le()},[]),a.useEffect(()=>{ie()},[l]);const Me=()=>{g<c.length&&!L&&(E(!0),setTimeout(()=>{F(r=>Math.min(r+10,c.length)),E(!1)},500))},je=r=>{const{scrollTop:K,scrollHeight:W,clientHeight:q}=r.target;W-K<=q+100&&Me()};return a.useEffect(()=>{s==="competitors"&&ue(!0)},[s]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6",children:e.jsx("div",{children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Ne,{className:"h-6 w-6 text-blue-600 mr-2"}),"Keyword Analysis"]})})}),e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsxs("button",{onClick:()=>n("performance"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="performance"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(be,{className:"h-4 w-4 inline mr-2"}),"Keyword Performance"]}),e.jsxs("button",{onClick:()=>n("competitors"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="competitors"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(Ce,{className:"h-4 w-4 inline mr-2"}),"Competitors"]})]})})]}),s==="performance"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-stretch md:items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 whitespace-nowrap",children:"Plugin:"}),e.jsxs("select",{value:l,onChange:r=>d(r.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"All plugins"}),t.map(r=>e.jsx("option",{value:r.pluginSlug,children:r.displayName},r.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[v.size>0&&e.jsxs("button",{onClick:()=>P(!0),className:"flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm",children:[e.jsx(De,{className:"h-4 w-4 mr-1"}),"Delete (",v.size,")"]}),e.jsxs("button",{onClick:()=>M(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(we,{className:"h-4 w-4 mr-1"}),"Add"]}),e.jsxs("button",{onClick:We,disabled:y,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(re,{className:`h-4 w-4 mr-1 ${y?"animate-spin":""}`}),y?"Refreshing...":"Refresh Ranks"]})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsxs("h3",{className:"text-base font-semibold text-gray-900",children:["Keywords",l&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["for"," ",(ye=t.find(r=>r.pluginSlug===l))==null?void 0:ye.displayName]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"text-sm text-gray-700",children:["Showing ",Math.min(g,c.length)," of"," ",c.length," keywords"]}),g<c.length&&e.jsx("div",{className:"text-sm text-blue-600",children:"Scroll down to load more..."})]})]}),$?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(re,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading keywords..."})]}):c.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"overflow-x-auto max-h-[470px] overflow-y-auto",onScroll:je,children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0 z-10",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12",children:e.jsx("input",{type:"checkbox",checked:c.slice(0,g).every(r=>v.has(r._id))&&c.length>0,onChange:r=>{const K=c.slice(0,g),W=new Set(v);r.target.checked?K.forEach(q=>W.add(q._id)):K.forEach(q=>W.delete(q._id)),I(W)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4",children:"Keyword"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Position"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Analytics"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Occurrences"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Tracked"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.slice(0,g).map((r,K)=>e.jsxs("tr",{className:K%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-12",children:e.jsx("input",{type:"checkbox",checked:v.has(r._id),onChange:W=>{const q=new Set(v);W.target.checked?q.add(r._id):q.delete(r._id),I(q)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-1/4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:r.keyword})}),r.source&&e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${r.source==="manual"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:r.source})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:r.position||"-"}),r.rankChange!==null&&r.rankChange!==void 0&&e.jsxs("span",{className:`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${r.rankChange<0?"bg-green-100 text-green-800":r.rankChange>0?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:[r.rankChange<0?"↑":r.rankChange>0?"↓":"=",Math.abs(r.rankChange)]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm w-20",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:()=>Ye(r),className:"inline-flex items-center p-1.5 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors",title:"View Rank Analytics",children:e.jsx(be,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>Be(r),className:"inline-flex items-center p-1.5 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors",title:"Search Related Plugins",children:e.jsx(Hs,{className:"h-4 w-4"})})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-20",children:de[r._id]||0}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:r.addedAt?new Date(r.addedAt).toLocaleDateString("en-GB"):"-"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:(()=>{const W=r.lastChecked||r.updatedAt;if(!W)return"-";const q=new Date(W);return isNaN(q.getTime())?"-":q.toLocaleDateString("en-GB")})()})]},r._id))})]})}),L&&e.jsxs("div",{className:"p-4 text-center",children:[e.jsx(re,{className:"h-5 w-5 text-blue-600 animate-spin mx-auto"}),e.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Loading more keywords..."})]}),g>=c.length&&c.length>10&&e.jsx("div",{className:"p-4 text-center",children:e.jsx("p",{className:"text-sm text-gray-500",children:"All keywords loaded"})})]}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ne,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Keywords Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:l?"No keywords added for this plugin yet.":"Please select a plugin first to view and manage keywords."}),l&&e.jsxs("button",{onClick:()=>M(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(we,{className:"h-4 w-4 mr-2"}),"Add First Keyword"]})]})]})]}),s==="competitors"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsx("h3",{className:"text-base font-semibold text-gray-900",children:"Competitor Plugins"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>ue(!0),disabled:z,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(re,{className:`h-4 w-4 mr-1 ${z?"animate-spin":""}`}),z?"Discovering...":"Discover"]}),e.jsxs("button",{onClick:()=>X(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(we,{className:"h-4 w-4 mr-1"}),"Add Manually"]})]})]}),z?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(re,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading competitors..."})]}):U.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Slug"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Rank"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tags"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added Date"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:U.map((r,K)=>{var W;return e.jsxs("tr",{className:K%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:r.pluginName})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:r.pluginSlug})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:r.currentRank?`#${r.currentRank}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:((W=r.activeInstalls)==null?void 0:W.toLocaleString())||"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[r.tags&&r.tags.length>0?r.tags.slice(0,3).map((q,J)=>e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",children:q},`${r._id}-tag-${J}`)):e.jsx("span",{className:"text-gray-400",children:"No tags"}),r.tags&&r.tags.length>3&&e.jsxs("span",{className:"text-xs text-gray-500",children:["+",r.tags.length-3," more"]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:new Date(r.createdAt).toLocaleDateString("en-GB")})]},r._id)})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ce,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Competitors Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Add keywords to automatically discover competitor plugins, or add competitors manually."}),e.jsxs("button",{onClick:()=>X(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(we,{className:"h-4 w-4 mr-2"}),"Add First Competitor"]})]})]}),e.jsx(ge,{isOpen:p,onClose:()=>{M(!1),w(""),o("")},title:"Add New Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Plugin"}),e.jsxs("select",{value:h,onChange:r=>o(r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Choose a plugin..."}),t.map(r=>e.jsx("option",{value:r.pluginSlug,children:r.displayName},r.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Keyword"}),e.jsx("input",{type:"text",value:k,onChange:r=>w(r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter keyword...",onKeyDown:r=>{r.key==="Enter"&&_e()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{M(!1),w(""),o("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:_e,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Keyword"})]})]})}),e.jsx(ge,{isOpen:T,onClose:()=>P(!1),title:"Delete Keywords",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-gray-600",children:["Are you sure you want to delete ",v.size," selected keyword(s)? This action cannot be undone and will also remove all related analytics data."]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>P(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Je,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keywords"})]})]})}),e.jsx(ge,{isOpen:R,onClose:()=>{_(!1),N(null)},title:"Delete Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-600",children:"Are you sure you want to delete this keyword? This action cannot be undone and will also remove all related analytics data."}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{_(!1),N(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Ue,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keyword"})]})]})}),e.jsx(ge,{isOpen:G,onClose:()=>{X(!1),oe("")},title:"Add Competitor Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsx("input",{type:"text",value:se,onChange:r=>oe(r.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter plugin slug...",onKeyDown:r=>{r.key==="Enter"&&Fe()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{X(!1),oe("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:Fe,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Competitor"})]})]})}),e.jsx(ge,{isOpen:pe,onClose:()=>{me(!1),Q(null),B([])},title:`Rank Analytics - ${(C==null?void 0:C.keyword)||""}`,children:e.jsxs("div",{className:"space-y-4",children:[ee?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(re,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading rank history..."})]}):m.length>0?e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Rank Trend (Last 30 days)"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Plugin:"})," ",C==null?void 0:C.pluginName]})]}),e.jsx("div",{className:"h-64",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(Ze,{data:m.slice().reverse().map(r=>({...r,displayDate:r.date,invertedRank:r.rank?-r.rank:0})),margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Re,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx($e,{dataKey:"displayDate",tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"}}),e.jsx(Te,{domain:["dataMin","dataMax"],tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"},tickFormatter:r=>`#${Math.abs(r)}`}),e.jsx(ke,{contentStyle:{backgroundColor:"#ffffff",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"12px"},formatter:r=>[`#${Math.abs(r)}`,"Rank"],labelFormatter:r=>`Date: ${r}`}),e.jsx(es,{type:"monotone",dataKey:"invertedRank",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2},children:e.jsx(Ee,{dataKey:"invertedRank",position:"top",formatter:r=>`#${Math.abs(r)}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(zs,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Rank History"}),e.jsx("p",{className:"text-gray-600",children:"No rank history found for this keyword."})]}),e.jsx("div",{className:"flex justify-end pt-4",children:e.jsx("button",{onClick:()=>{me(!1),Q(null),B([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})}),e.jsx(ge,{isOpen:Y,onClose:()=>{i(!1),Q(null),O([])},title:`Related Plugins - "${(C==null?void 0:C.keyword)||""}"`,maxWidth:"max-w-6xl",fixedHeight:!0,children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx("div",{className:"flex-1 overflow-y-auto",children:ae?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(re,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading related plugins..."})]}):A.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rating"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tested Up To"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Update"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:A.map((r,K)=>e.jsxs("tr",{className:K%2===0?"bg-white":"bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap",children:[e.jsxs("a",{href:r.wordpressUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 font-medium flex items-center",title:js(r.pluginName),children:[jt(r.pluginName),e.jsx(ze,{className:"h-3 w-3 ml-1"})]}),e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:r.pluginSlug})]}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:r.activeInstalls>0?r.activeInstalls.toLocaleString():"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(He,{className:"h-4 w-4 text-yellow-400 mr-1"}),e.jsxs("span",{children:[r.rating>0?r.rating.toFixed(1):"N/A",r.numRatings>0&&e.jsxs("span",{className:"text-xs text-gray-400 ml-1",children:["(",r.numRatings,")"]})]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:r.testedUpTo!=="N/A"?`WP ${r.testedUpTo}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:r.lastUpdated!=="N/A"?(()=>{const W=new Date(r.lastUpdated);if(isNaN(W.getTime()))return"N/A";const q=W.getDate().toString().padStart(2,"0"),J=(W.getMonth()+1).toString().padStart(2,"0"),le=W.getFullYear();return`${J}-${q}-${le}`})():"N/A"})]},r.pluginSlug))})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ne,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Related Plugins Found"}),e.jsx("p",{className:"text-gray-600",children:"No plugins found containing this keyword."})]})}),e.jsx("div",{className:"flex justify-end pt-4 border-t border-gray-200 flex-shrink-0",children:e.jsx("button",{onClick:()=>{i(!1),Q(null),O([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})})]})},Nt=()=>{const[s,n]=a.useState("downloaded-data"),[t,f]=a.useState([]),[l,d]=a.useState(!1),[c,b]=a.useState(""),[$,j]=a.useState([]),[p,M]=a.useState(!1),[k,w]=a.useState("15"),[h,o]=a.useState({start:"",end:""}),[y,D]=a.useState(""),[v,I]=a.useState([]),[T,P]=a.useState(!1),[S,u]=a.useState({startDate:"",endDate:"",rating:[],page:1}),[x,N]=a.useState({totalReviews:0,averageRating:0,ratingDistribution:{}});a.useEffect(()=>{R()},[]);const R=async()=>{try{d(!0);const U=localStorage.getItem("adminToken"),z=await fetch("https://pluginsight.vercel.app/api/analytics/added-plugins",{headers:{Authorization:`Bearer ${U}`,"Content-Type":"application/json"}});if(!z.ok)throw new Error(`HTTP error! status: ${z.status}`);const V=await z.json();V.success?f(V.plugins):console.warn("Failed to load added plugins:",V.message)}catch(U){console.error("Error loading added plugins:",U),U.name==="TypeError"&&U.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins","error")}finally{d(!1)}},_=[{id:"downloaded-data",name:"Downloaded Data",icon:xe},{id:"plugin-reviews",name:`Plugin Reviews${x.totalReviews>0?` (${x.totalReviews})`:""}`,icon:Ge}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(os,{className:"h-6 w-6 text-blue-600 mr-2"}),"Plugin Data Analysis"]})})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8 px-6",children:_.map(U=>{const H=U.icon;return e.jsxs("button",{onClick:()=>n(U.id),className:`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${s===U.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(H,{className:"h-4 w-4"}),e.jsx("span",{children:U.name})]},U.id)})})}),e.jsxs("div",{className:"p-6",children:[s==="downloaded-data"&&e.jsx(vt,{addedPlugins:t,selectedPlugin:c,setSelectedPlugin:b,downloadData:$,setDownloadData:j,downloadLoading:p,setDownloadLoading:M,dateRange:k,setDateRange:w,customDateRange:h,setCustomDateRange:o}),s==="plugin-reviews"&&e.jsx(kt,{addedPlugins:t,reviewsPlugin:y,setReviewsPlugin:D,reviews:v,setReviews:I,reviewsLoading:T,setReviewsLoading:P,reviewFilters:S,setReviewFilters:u,reviewStats:x,setReviewStats:N})]})]})]})},vt=({addedPlugins:s,selectedPlugin:n,setSelectedPlugin:t,downloadData:f,setDownloadData:l,downloadLoading:d,setDownloadLoading:c,dateRange:b,setDateRange:$,customDateRange:j,setCustomDateRange:p})=>{var w;const M=async()=>{try{c(!0),window.toast("Starting plugin download data fetch...","info");const h=localStorage.getItem("adminToken"),D=await(await fetch("https://pluginsight.vercel.app/api/analytics/download-data/refresh",{method:"POST",headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}})).json();D.success?(window.toast(D.message,"success"),n&&k(n)):window.toast(D.message||"Failed to refresh download data","error")}catch(h){console.error("Error refreshing download data:",h),window.toast("Failed to refresh download data","error")}finally{c(!1)}},k=async h=>{if(h)try{c(!0);const o=localStorage.getItem("adminToken"),y="https://pluginsight.vercel.app";let D=`${y}/api/analytics/download-data/${h}?days=${b}`;b==="custom"&&j.start&&j.end&&(D=`${y}/api/analytics/download-data/${h}?startDate=${j.start}&endDate=${j.end}`);const I=await(await fetch(D,{headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).json();if(I.success){const T=I.downloadData.map(P=>({date:new Date(P.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:P.downloads,fullDate:P.date}));l(T)}}catch(o){console.error("Error loading download data:",o),window.toast("Failed to load download data","error")}finally{c(!1)}};return Xe.useEffect(()=>{n&&k(n)},[n,b,j]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:n,onChange:h=>t(h.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(h=>e.jsx("option",{value:h.pluginSlug,children:h.displayName||h.pluginName},h.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:b,onChange:h=>$(h.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom Range"})]})]}),b==="custom"&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:j.start,onChange:h=>p(o=>({...o,start:h.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:j.end,onChange:h=>p(o=>({...o,end:h.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsxs("button",{onClick:M,disabled:d,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(re,{className:`h-4 w-4 mr-2 ${d?"animate-spin":""}`}),"Refresh"]})]}),n&&f.length>0?e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Download Trends -"," ",((w=s.find(h=>h.pluginSlug===n))==null?void 0:w.displayName)||n]}),e.jsx("div",{className:"h-96",children:e.jsx(ve,{width:"100%",height:"100%",children:e.jsxs(us,{data:f,children:[e.jsx(Re,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx($e,{dataKey:"date",stroke:"#6b7280",fontSize:12}),e.jsx(Te,{stroke:"#6b7280",fontSize:12,tickFormatter:h=>h.toLocaleString()}),e.jsx(ke,{contentStyle:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"8px"},formatter:h=>[h.toLocaleString(),"Downloads"],labelFormatter:(h,o)=>o&&o[0]?new Date(o[0].payload.fullDate).toLocaleDateString("en-GB",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):h}),e.jsx(xs,{dataKey:"downloads",fill:"#3b82f6",radius:[4,4,0,0],children:e.jsx(Ee,{dataKey:"downloads",position:"top",fontSize:10,fill:"#3b82f6",formatter:h=>h.toLocaleString()})})]})})})]}):n&&d?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(re,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading download data..."})]})}):n?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(xe,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Download Data"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No download data found for this plugin. Click refresh to fetch the latest data."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(xe,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its download trends."})]})})]})},kt=({addedPlugins:s,reviewsPlugin:n,setReviewsPlugin:t,reviews:f,setReviews:l,reviewsLoading:d,setReviewsLoading:c,reviewFilters:b,setReviewFilters:$,reviewStats:j,setReviewStats:p})=>{const M=async()=>{try{c(!0),window.toast("Starting plugin reviews fetch...","info");const o=localStorage.getItem("adminToken"),D=await fetch("https://pluginsight.vercel.app/api/analytics/reviews/refresh",{method:"POST",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}});if(!D.ok)throw new Error(`HTTP error! status: ${D.status}`);const v=await D.json();v.success?(window.toast(v.message,"success"),n&&k(n)):window.toast(v.message||"Failed to refresh reviews","error")}catch(o){console.error("Error refreshing reviews:",o),o.name==="TypeError"&&o.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to refresh reviews","error")}finally{c(!1)}},k=async(o,y=1)=>{var D;if(o)try{c(!0);const v=new URLSearchParams({page:y.toString(),limit:"20"});b.startDate&&v.append("startDate",b.startDate),b.endDate&&v.append("endDate",b.endDate),b.rating.length>0&&b.rating.forEach(P=>v.append("rating",P.toString())),console.log(`Loading reviews for plugin: ${o} with params:`,v.toString());const I=await ys(`/api/analytics/reviews/${o}?${v}`);if(!I.ok)throw new Error(`HTTP error! status: ${I.status}`);const T=await I.json();console.log("Reviews API response:",T),T.success?(console.log(`Loaded ${((D=T.reviews)==null?void 0:D.length)||0} reviews for ${o} (page ${y})`),l(y===1?T.reviews||[]:P=>[...P,...T.reviews||[]]),p(T.stats||{})):(console.warn("Reviews API returned success: false",T),y===1&&l([]),p({}))}catch(v){console.error("Error loading reviews:",v),v.name==="TypeError"&&v.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast(`Failed to load reviews: ${v.message}`,"error"),y===1&&(l([]),p({}))}finally{c(!1)}};Xe.useEffect(()=>{n&&k(n)},[n,b]);const w=o=>Array.from({length:5},(y,D)=>e.jsx("span",{className:`text-lg ${D<o?"text-yellow-400":"text-gray-300"}`,children:"★"},D)),h=o=>{if(!o)return"";let y=o.replace(/<!\[CDATA\[/g,"").replace(/\]\]>/g,"").trim();y=y.replace(/^.*?Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/^.*?Replies:\s*\d+\s*/gi,"").replace(/^.*?Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*/gi,"").replace(/Rating:\s*\d+\s*stars?\s*/gi,"").trim();const D=document.createElement("textarea");return D.innerHTML=y,y=D.value,y=y.replace(/<[^>]*>/g,""),y=y.replace(/\s+/g," ").trim(),y};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:n,onChange:o=>t(o.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(o=>e.jsx("option",{value:o.pluginSlug,children:o.displayName||o.pluginName},o.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:b.startDate,onChange:o=>$(y=>({...y,startDate:o.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:b.endDate,onChange:o=>$(y=>({...y,endDate:o.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rating Filter"}),e.jsxs("select",{value:b.rating.length===1?b.rating[0]:"",onChange:o=>{const y=o.target.value;$(y===""?D=>({...D,rating:[]}):D=>({...D,rating:[parseInt(y)]}))},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"All ratings"}),e.jsx("option",{value:"5",children:"5 stars"}),e.jsx("option",{value:"4",children:"4 stars"}),e.jsx("option",{value:"3",children:"3 stars"}),e.jsx("option",{value:"2",children:"2 stars"}),e.jsx("option",{value:"1",children:"1 star"})]})]})]}),e.jsxs("button",{onClick:M,disabled:d,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(re,{className:`h-4 w-4 mr-2 ${d?"animate-spin":""}`}),"Refresh"]})]}),n&&f.length>0?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"text-sm text-gray-600 flex items-center gap-2",children:["Total",e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:j.totalReviews}),"Reviews"]})})})}),e.jsx("div",{className:"h-[460px] overflow-y-auto space-y-4",children:f.map((o,y)=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 animate-fade-in-up",style:{animationDelay:`${y*100}ms`,animationFillMode:"both"},children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex",children:w(o.rating)}),e.jsxs("div",{className:"text-sm text-gray-500",children:["by ",o.author," •"," ",new Date(o.date).toLocaleDateString("en-GB")]})]}),o.reviewUrl&&e.jsx("a",{href:o.reviewUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-blue-500 rounded-lg hover:text-blue-700 transition-all duration-200",children:e.jsx(ze,{className:"h-4 w-4"})})]}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:o.title}),e.jsx("div",{className:"text-gray-700 leading-relaxed",children:h(o.content)})]},o._id||y))})]}):n&&d?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(re,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):n?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ge,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Reviews Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No reviews found for this plugin. Click refresh to fetch the latest reviews."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ge,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its reviews."})]})})]})},St=()=>{const{user:s}=fe(),[n,t]=a.useState([]),[f,l]=a.useState(!0),[d,c]=a.useState(""),[b,$]=a.useState(""),[j,p]=a.useState(""),[M,k]=a.useState(!1),[w,h]=a.useState(!1),[o,y]=a.useState(!1),[D,v]=a.useState(!1),[I,T]=a.useState(!1),[P,S]=a.useState(null),[u,x]=a.useState({current:1,pages:1,total:0,limit:10}),[N,R]=a.useState({name:"",email:"",password:"",role:"member"}),[_,U]=a.useState({newPassword:"",confirmPassword:""}),[H,z]=a.useState({canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1}),V=async(i=1,C="",Q="")=>{try{l(!0);const m=localStorage.getItem("adminToken");if(!m){c("Authentication token not found. Please login again.");return}const B=new URLSearchParams({page:i.toString(),limit:"10",...C&&{search:C},...Q&&{role:Q}}),O=await fetch(`https://pluginsight.vercel.app/api/users?${B}`,{headers:{Authorization:`Bearer ${m}`,"Content-Type":"application/json"}});if(!O.ok){if(O.status===401){c("Authentication failed. Please login again."),localStorage.removeItem("adminToken");return}throw new Error(`HTTP error! status: ${O.status}`)}const ee=await O.json();ee.success?(t(ee.users),x(ee.pagination),c("")):c(ee.message||"Failed to fetch users")}catch(m){console.error("Fetch users error:",m),m.name==="TypeError"&&m.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to fetch users. Please try again.")}finally{l(!1)}};a.useEffect(()=>{V()},[]);const G=()=>{V(1,b,j)},X=async i=>{i.preventDefault();try{const C=localStorage.getItem("adminToken"),m=await fetch("https://pluginsight.vercel.app/api/users",{method:"POST",headers:{Authorization:`Bearer ${C}`,"Content-Type":"application/json"},body:JSON.stringify(N)});if(!m.ok)throw new Error(`HTTP error! status: ${m.status}`);const B=await m.json();B.success?(k(!1),R({name:"",email:"",password:"",role:"member"}),V(u.current,b,j)):c(B.message||"Failed to create user")}catch(C){console.error("Add user error:",C),C.name==="TypeError"&&C.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to create user")}},se=async i=>{i.preventDefault();try{const C=localStorage.getItem("adminToken"),m=await fetch(`https://pluginsight.vercel.app/api/users/${P._id}`,{method:"PUT",headers:{Authorization:`Bearer ${C}`,"Content-Type":"application/json"},body:JSON.stringify({name:N.name,email:N.email,role:N.role,isActive:N.isActive})});if(!m.ok)throw new Error(`HTTP error! status: ${m.status}`);const B=await m.json();B.success?(h(!1),S(null),V(u.current,b,j)):c(B.message||"Failed to update user")}catch(C){console.error("Edit user error:",C),C.name==="TypeError"&&C.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to update user")}},oe=i=>{if(s&&s._id===i._id){window.toast("You cannot delete your own account","error");return}S(i),v(!0)},de=async()=>{if(P)try{const i=localStorage.getItem("adminToken"),Q=await fetch(`https://pluginsight.vercel.app/api/users/${P._id}`,{method:"DELETE",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}});if(!Q.ok)throw new Error(`HTTP error! status: ${Q.status}`);const m=await Q.json();m.success?(V(u.current,b,j),v(!1),S(null)):c(m.message||"Failed to delete user")}catch(i){console.error("Delete user error:",i),i.name==="TypeError"&&i.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to delete user")}},ne=async i=>{if(i.preventDefault(),_.newPassword!==_.confirmPassword){c("Passwords do not match");return}try{const C=localStorage.getItem("adminToken"),m=await fetch(`https://pluginsight.vercel.app/api/users/${P._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${C}`,"Content-Type":"application/json"},body:JSON.stringify({newPassword:_.newPassword})});if(!m.ok)throw new Error(`HTTP error! status: ${m.status}`);const B=await m.json();B.success?(y(!1),S(null),U({newPassword:"",confirmPassword:""}),alert("Password reset successfully")):c(B.message||"Failed to reset password")}catch(C){console.error("Reset password error:",C),C.name==="TypeError"&&C.message.includes("Failed to fetch")?c("Unable to connect to server. Please check if the backend is running."):c("Failed to reset password")}},pe=i=>{S(i),R({name:i.name,email:i.email,role:i.role,isActive:i.isActive}),h(!0)},me=i=>{S(i),U({newPassword:"",confirmPassword:""}),y(!0)},g=i=>{var C,Q,m,B;S(i),z({canAddPlugins:((C=i.permissions)==null?void 0:C.canAddPlugins)||!1,canDeletePlugins:((Q=i.permissions)==null?void 0:Q.canDeletePlugins)||!1,canAddKeywords:((m=i.permissions)==null?void 0:m.canAddKeywords)||!1,canAddUsers:((B=i.permissions)==null?void 0:B.canAddUsers)||!1}),T(!0)},F=i=>{switch(i){case"superadmin":return"bg-red-100 text-red-800";case"admin":return"bg-blue-100 text-blue-800";case"member":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},L=i=>s.role==="superadmin"||s.role==="admin"&&i.role==="member"?s._id!==i._id:!1,E=i=>!(s.role==="admin"&&i.role==="superadmin"),Y=i=>s._id===i._id?!1:s.role==="superadmin"||s.role==="admin"&&i.role==="member";return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center p-4",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Ks,{className:"h-6 w-6 text-blue-600 mr-2"}),"Team Members"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage system users and their permissions"})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200 flex justify-between items-center",children:[e.jsx("div",{children:["admin","superadmin"].includes(s==null?void 0:s.role)&&e.jsxs("button",{onClick:()=>k(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[e.jsx(we,{className:"h-4 w-4"}),"Add User"]})}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"flex relative",children:[e.jsx(Ne,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:b,onChange:i=>$(i.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Vs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsxs("select",{value:j,onChange:i=>p(i.target.value),className:"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Roles"}),e.jsx("option",{value:"superadmin",children:"Super Admin"}),e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"})]})]}),e.jsx("button",{onClick:G,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Search"})]})]}),d&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:d}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:f?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Loading users..."})]}):n.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ce,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:n.filter(E).map(i=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:i.name}),e.jsx("div",{className:"text-sm text-gray-500",children:i.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${F(i.role)}`,children:i.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${i.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:i.isActive?"Active":"Inactive"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(i.createdAt).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx("div",{className:"flex items-center gap-2",children:L(i)&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>pe(i),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:e.jsx(ds,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>me(i),className:"text-yellow-600 hover:text-yellow-900",title:"Reset Password",children:e.jsx(qs,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>g(i),className:"text-purple-600 hover:text-purple-900",title:"Manage Permissions",children:e.jsx(Qe,{className:"h-4 w-4"})}),Y(i)&&e.jsx("button",{onClick:()=>oe(i),className:"text-red-600 hover:text-red-900",title:"Delete User",children:e.jsx(De,{className:"h-4 w-4"})})]})})})]},i._id))})]})})}),u.pages>1&&e.jsxs("div",{className:"flex justify-center items-center gap-2",children:[e.jsx("button",{onClick:()=>V(u.current-1,b,j),disabled:u.current===1,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Previous"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",u.current," of ",u.pages]}),e.jsx("button",{onClick:()=>V(u.current+1,b,j),disabled:u.current===u.pages,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Next"})]}),M&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Add New User"}),e.jsxs("form",{onSubmit:X,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:N.name,onChange:i=>R({...N,name:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:N.email,onChange:i=>R({...N,email:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{type:"password",value:N.password,onChange:i=>R({...N,password:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:N.role,onChange:i=>R({...N,role:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{k(!1),R({name:"",email:"",password:"",role:"member"})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Add User"})]})]})]})}),w&&P&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Edit User"}),e.jsxs("form",{onSubmit:se,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:N.name,onChange:i=>R({...N,name:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:N.email,onChange:i=>R({...N,email:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:N.role,onChange:i=>R({...N,role:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:(s==null?void 0:s.role)==="admin"&&["admin","superadmin"].includes(P.role),children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsx("div",{children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:N.isActive,onChange:i=>R({...N,isActive:i.target.checked}),className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{h(!1),S(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Update User"})]})]})]})}),o&&P&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Reset Password"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["Reset password for: ",e.jsx("strong",{children:P.name})]}),e.jsxs("form",{onSubmit:ne,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),e.jsx("input",{type:"password",value:_.newPassword,onChange:i=>U({..._,newPassword:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",value:_.confirmPassword,onChange:i=>U({..._,confirmPassword:i.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{y(!1),S(null),U({newPassword:"",confirmPassword:""})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:"Reset Password"})]})]})]})}),D&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(Ws,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Delete User"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this user? This action cannot be undone."})]})]}),P&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:P.name}),e.jsx("p",{className:"text-sm text-gray-500",children:P.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${F(P.role)}`,children:P.role})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{v(!1),S(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:de,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete User"})]})]})}),e.jsx(ge,{isOpen:I,onClose:()=>{T(!1),S(null)},title:"Manage User Permissions",children:P&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:P.name}),e.jsx("p",{className:"text-sm text-gray-500",children:P.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${F(P.role)}`,children:P.role})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Plugins"}),e.jsx("input",{type:"checkbox",checked:H.canAddPlugins,onChange:i=>z(C=>({...C,canAddPlugins:i.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Delete Plugins"}),e.jsx("input",{type:"checkbox",checked:H.canDeletePlugins,onChange:i=>z(C=>({...C,canDeletePlugins:i.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Keywords"}),e.jsx("input",{type:"checkbox",checked:H.canAddKeywords,onChange:i=>z(C=>({...C,canAddKeywords:i.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Users"}),e.jsx("input",{type:"checkbox",checked:H.canAddUsers,onChange:i=>z(C=>({...C,canAddUsers:i.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{onClick:()=>{T(!1),S(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:()=>{window.toast("Permissions updated successfully","success"),T(!1),S(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Save Permissions"})]})]})})]})},At=()=>{const{user:s}=fe(),[n,t]=a.useState(!1),[f,l]=a.useState(""),[d,c]=a.useState(""),[b,$]=a.useState({member:{canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1},admin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0},superadmin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0}});a.useEffect(()=>{j()},[]);const j=async()=>{try{const o=localStorage.getItem("adminToken"),v=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).json();v.success&&$(v.permissions)}catch(o){console.error("Error loading permissions:",o)}},p=(o,y,D)=>{$(v=>({...v,[o]:{...v[o],[y]:D}}))},M=async()=>{t(!0),c(""),l("");try{const o=localStorage.getItem("adminToken"),v=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{method:"PUT",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"},body:JSON.stringify({permissions:b})})).json();v.success?(l("Permissions updated successfully"),setTimeout(()=>l(""),3e3)):c(v.message||"Failed to update permissions")}catch(o){console.error("Error saving permissions:",o),c("Failed to update permissions")}finally{t(!1)}},k={canAddPlugins:"Add Plugins",canDeletePlugins:"Delete Plugins",canAddKeywords:"Add Keywords",canAddUsers:"Add Users"},w={member:"Member",admin:"Admin",superadmin:"Super Admin"};if(!s)return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-white rounded-lg p-8 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Oe,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600",children:"Please login to access settings."})]})})});const h=["admin","superadmin"].includes(s.role);return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Qe,{className:"h-8 w-8 text-blue-600 mr-3"}),"Settings"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Manage application settings and user permissions"})]})})}),f&&e.jsx("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg",children:f}),d&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:d}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 w-1/2",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(Ce,{className:"h-5 w-5 text-gray-600 mr-2"}),"User Permissions"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Configure what actions each user role can perform"})]}),e.jsx("div",{className:"flex justify-end",children:h?e.jsxs("button",{onClick:M,disabled:n,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[e.jsx(ms,{className:"h-4 w-4 mr-2"}),n?"Saving...":"Save"]}):e.jsx("div",{className:"text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-lg",children:"View Only - Admin privileges required to edit"})})]}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Permission"}),Object.keys(w).map(o=>e.jsx("th",{className:"text-center py-3 px-4 font-medium text-gray-900",children:w[o]},o))]})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:Object.keys(k).map(o=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"py-4 px-4 text-sm font-medium text-gray-900",children:k[o]}),Object.keys(w).map(y=>{var D;return e.jsx("td",{className:"py-4 px-4 text-center",children:e.jsx("input",{type:"checkbox",checked:((D=b[y])==null?void 0:D[o])||!1,onChange:v=>p(y,o,v.target.checked),disabled:!h||y==="superadmin",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"})},y)})]},o))})]})})})]})]})},Pt=()=>{const{user:s}=fe(),[n,t]=a.useState(!1),[f,l]=a.useState(!1),[d,c]=a.useState({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),b=async k=>{k.preventDefault(),l(!0);try{if(d.newPassword){if(d.newPassword!==d.confirmPassword){window.toast("New passwords do not match","error"),l(!1);return}if(d.newPassword.length<6){window.toast("Password must be at least 6 characters","error"),l(!1);return}}const w=localStorage.getItem("adminToken"),h="https://pluginsight.vercel.app",o={name:d.name,email:d.email},y=await fetch(`${h}/api/users/${s.id||s._id}`,{method:"PUT",headers:{Authorization:`Bearer ${w}`,"Content-Type":"application/json"},body:JSON.stringify(o)});if(!y.ok)throw new Error(`HTTP error! status: ${y.status}`);const D=await y.json();if(!D.success){window.toast(D.message||"Failed to update profile","error"),l(!1);return}if(d.newPassword){const I={newPassword:d.newPassword},T=await fetch(`${h}/api/users/${s.id||s._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${w}`,"Content-Type":"application/json"},body:JSON.stringify(I)});if(!T.ok)throw new Error(`HTTP error! status: ${T.status}`);const P=await T.json();if(!P.success){window.toast(P.message||"Failed to update password","error"),l(!1);return}}const v={...s,...D.user};localStorage.setItem("adminUser",JSON.stringify(v)),window.toast("Profile updated successfully","success"),t(!1),c(I=>({...I,name:v.name,email:v.email,newPassword:"",confirmPassword:""})),window.location.reload()}catch(w){console.error("Error updating profile:",w),w.name==="TypeError"&&w.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to update profile","error")}finally{l(!1)}},$=()=>{c({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),t(!1)},j=k=>{c({...d,[k.target.name]:k.target.value})},p=k=>{switch(k){case"superadmin":return e.jsx(Oe,{className:"h-5 w-5 text-yellow-600"});case"admin":return e.jsx(Oe,{className:"h-5 w-5 text-blue-600"});default:return e.jsx(Pe,{className:"h-5 w-5 text-gray-600"})}},M=k=>{const w={superadmin:"bg-yellow-100 text-yellow-800 border-yellow-200",admin:"bg-blue-100 text-blue-800 border-blue-200",member:"bg-gray-100 text-gray-800 border-gray-200"};return e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${w[k]}`,children:[p(k),e.jsx("span",{className:"ml-2 capitalize",children:k})]})};return s?e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Profile Settings"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your account information and preferences"})]}),e.jsx("div",{className:"",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"h-24 w-24 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4",children:e.jsx(Pe,{className:"h-12 w-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:s==null?void 0:s.name}),e.jsx("p",{className:"text-blue-100 mb-4",children:s==null?void 0:s.email}),M(s==null?void 0:s.role)]})})}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Personal Information"}),n?e.jsxs("button",{onClick:$,className:"flex items-center space-x-2 px-4 py-2 bg-gray-50 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:[e.jsx(Ve,{className:"h-4 w-4"}),e.jsx("span",{children:"Cancel"})]}):e.jsxs("button",{onClick:()=>t(!0),className:"flex items-center space-x-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",children:[e.jsx(ds,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]}),e.jsxs("form",{onSubmit:b,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Pe,{className:"h-4 w-4 mr-2"}),"Full Name"]}),e.jsx("input",{type:"text",name:"name",value:d.name,onChange:j,disabled:!n,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(is,{className:"h-4 w-4 mr-2"}),"Email Address"]}),e.jsx("input",{type:"email",name:"email",value:d.email,onChange:j,disabled:!n,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Oe,{className:"h-4 w-4 mr-2"}),"Role"]}),e.jsx("div",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500",children:e.jsx("span",{className:"capitalize",children:s==null?void 0:s.role})}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Role can only be changed by administrators"})]}),n&&e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),e.jsx("input",{type:"password",name:"newPassword",value:d.newPassword,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter new password"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),e.jsx("input",{type:"password",name:"confirmPassword",value:d.confirmPassword,onChange:j,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Confirm new password"})]})]})]}),n&&e.jsx("div",{className:"flex justify-end pt-6",children:e.jsxs("button",{type:"submit",disabled:f,className:"flex items-center space-x-2 px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ms,{className:"h-4 w-4"}),e.jsx("span",{children:f?"Saving...":"Save"})]})})]})]})})]})})})]})}):e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading profile..."})]})})},Ct=["p","br","strong","b","em","i","u","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","pre","code","a","img","div","span","table","thead","tbody","tr","td","th","iframe","video","source"],Dt={a:["href","title","target","rel"],img:["src","alt","title","width","height","class"],iframe:["src","width","height","frameborder","allowfullscreen","title","class"],video:["src","width","height","controls","autoplay","muted","loop","poster","class"],source:["src","type"],div:["class","id"],span:["class","id"],p:["class"],h1:["class"],h2:["class"],h3:["class"],h4:["class"],h5:["class"],h6:["class"],ul:["class"],ol:["class"],li:["class"],table:["class"],thead:["class"],tbody:["class"],tr:["class"],td:["class"],th:["class"],blockquote:["class"],pre:["class"],code:["class"]};function Rt(s){if(!s)return"";const n=document.createElement("div");n.innerHTML=s;function t(l){const d=l.tagName.toLowerCase();if(!Ct.includes(d)){l.remove();return}if(d==="iframe"){const j=l.getAttribute("src"),p=l.getAttribute("title")||"Video content",M=document.createElement("div");M.className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center my-4";let k="Video",w=`<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
      </svg>`;j&&j.includes("youtube")?(k="YouTube Video",w=`<svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
        </svg>`):j&&j.includes("vimeo")&&(k="Vimeo Video",w=`<svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.977 6.416c-.105 2.338-1.739 5.543-4.894 9.609-3.268 4.247-6.026 6.37-8.29 6.37-1.409 0-2.578-1.294-3.553-3.881L5.322 11.4C4.603 8.816 3.834 7.522 3.01 7.522c-.179 0-.806.378-1.881 1.132L0 7.197a315.065 315.065 0 0 0 4.192-3.729C5.978 2.4 7.333 1.718 8.222 1.718c2.104 0 3.391 1.262 3.863 3.783.508 2.27.861 3.683.861 4.235 0 1.288-.547 3.2-1.642 5.737-.832 1.96-1.747 2.94-2.747 2.94-.842 0-1.638-.79-2.387-2.37l-.318-.81c-.613-1.96-1.17-2.94-1.668-2.94-.498 0-1.225.562-2.178 1.688l-.951-1.4c1.588-1.96 3.176-2.94 4.764-2.94 1.588 0 2.823 1.225 3.706 3.676.883 2.45 1.225 3.676 1.225 3.676s.342 1.96 1.026 5.88c.684 3.92 1.026 5.88 1.026 5.88.342 1.96 1.026 2.94 2.052 2.94 1.026 0 2.394-.98 4.104-2.94 1.71-1.96 2.565-3.92 2.565-5.88z"/>
        </svg>`),M.innerHTML=`
        <div class="flex flex-col items-center space-y-3">
          ${w}
          <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-1">${k}</h4>
            <p class="text-gray-600 text-sm mb-3">${p}</p>
            <a href="${j}" target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Watch Video
            </a>
          </div>
        </div>
      `,l.parentNode.replaceChild(M,l);return}const c=Dt[d]||[];Array.from(l.attributes).forEach(j=>{c.includes(j.name)||l.removeAttribute(j.name)}),Array.from(l.children).forEach(j=>t(j))}return Array.from(n.children).forEach(l=>t(l)),n.innerHTML}function Ie(s){if(!s)return"";let n=s.replace(/\[video\s+([^\]]+)\]/g,(t,f)=>{const l=f.match(/src="([^"]+)"/);return l?`<video controls><source src="${l[1]}" type="video/mp4"></video>`:""}).replace(/\[youtube\s+([^\]]+)\]/g,(t,f)=>{const l=f.match(/(?:id="|v=)([^"&\s]+)/);return l?`<iframe src="https://www.youtube.com/embed/${l[1]}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`:""}).replace(/\[vimeo\s+([^\]]+)\]/g,(t,f)=>{const l=f.match(/id="?([^"\s]+)"?/);return l?`<iframe src="https://player.vimeo.com/video/${l[1]}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`:""}).replace(/https?:\/\/(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/g,(t,f)=>`<iframe src="https://www.youtube.com/embed/${f}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g,(t,f)=>`<iframe src="https://www.youtube.com/embed/${f}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/(?:www\.)?vimeo\.com\/(\d+)/g,(t,f)=>`<iframe src="https://player.vimeo.com/video/${f}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`);return Rt(n)}const $t=()=>{const{slug:s}=Ss(),n=Ke(),[t,f]=a.useState(null),[l,d]=a.useState(null),[c,b]=a.useState(!0),[$,j]=a.useState(""),[p,M]=a.useState({}),[k,w]=a.useState(""),[h,o]=a.useState(!0),[y,D]=a.useState(!1),v=async()=>{try{o(!0);const x=await fetch(`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&slug=${t.slug}`);if(!x.ok)throw new Error("Failed to fetch plugin information");const N=await x.json();N.versions&&M(N.versions)}catch(x){console.error("Error fetching versions data:",x),M({})}finally{o(!1)}};a.useEffect(()=>{t&&v()},[t]),a.useEffect(()=>{I()},[s]);const I=async()=>{try{b(!0);const x=localStorage.getItem("adminToken"),N="https://pluginsight.vercel.app",[R,_]=await Promise.all([fetch(`${N}/api/plugins/${s}`,{headers:{Authorization:`Bearer ${x}`}}),fetch(`${N}/api/analytics/plugin-info/${s}`,{headers:{Authorization:`Bearer ${x}`}})]);if(!R.ok)throw new Error("Failed to fetch plugin details");const U=await R.json();if(!U.success){j(U.message||"Plugin not found");return}if(f(U.plugin),_.ok){const H=await _.json();H.success&&H.pluginInfo?d(H.pluginInfo):console.log("No plugin information found in database for:",s)}else console.log("Failed to fetch plugin information from database")}catch(x){console.error("Error fetching plugin details:",x),j("Failed to load plugin details")}finally{b(!1)}},T=x=>{if(!x)return"N/A";const N=x.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!N)return"N/A";const[,R,_,U]=N;return`${U}-${_}-${R}`},P=x=>{const N=Math.round((x||0)/20);return[...Array(5)].map((R,_)=>e.jsx(He,{className:`h-4 w-4 ${_<N?"text-yellow-400 fill-current":"text-gray-300"}`},_))},S=x=>l&&l[x]!==void 0&&l[x]!==null?l[x]:t!=null&&t.pluginData&&t.pluginData[x]!==void 0&&t.pluginData[x]!==null?t.pluginData[x]:null,u=x=>{const N=x.split(".");if(l){let R=l;for(const _ of N)if(R&&typeof R=="object"&&R[_]!==void 0)R=R[_];else{R=null;break}if(R!==null)return R}if(t!=null&&t.pluginData){let R=t.pluginData;for(const _ of N)if(R&&typeof R=="object"&&R[_]!==void 0)R=R[_];else{R=null;break}return R}return null};return c?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):$||!t?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-red-600 text-xl mb-4",children:$}),e.jsx("button",{onClick:()=>n(-1),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Go Back"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("button",{onClick:()=>n(-1),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[e.jsx(Js,{className:"h-5 w-5"}),e.jsx("span",{children:"Back"})]}),e.jsx("div",{className:"h-6 w-px bg-gray-300"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Plugin Details"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[S("homepage")&&e.jsxs("button",{onClick:()=>window.open(S("homepage"),"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(ze,{className:"h-4 w-4"}),e.jsx("span",{children:"Home page"})]}),e.jsxs("button",{onClick:()=>window.open(`https://wordpress.org/plugins/${t.slug}/`,"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(ze,{className:"h-4 w-4"}),e.jsx("span",{children:"View on WordPress.org"})]})]})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[u("icons.2x")&&e.jsx("img",{src:u("icons.2x"),alt:t.name,className:"w-16 h-16 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:t.name}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[P(S("rating")),e.jsxs("span",{className:"text-sm text-gray-600 ml-2",children:["(",S("num_ratings")||0," ratings)"]})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[e.jsx(xe,{className:"h-4 w-4"}),e.jsxs("span",{children:[(S("downloaded")||0).toLocaleString()," ","downloads"]})]})]})]})]})}),u("sections.description")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Description"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ie(u("sections.description"))}})]}),u("sections.installation")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Installation"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ie(u("sections.installation"))}})]}),u("sections.faq")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Frequently Asked Questions"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Ie(u("sections.faq"))}})]}),u("sections.changelog")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Changelog"}),e.jsx("div",{className:"prose max-w-none text-gray-700 max-h-96 overflow-y-auto",dangerouslySetInnerHTML:{__html:Ie(u("sections.changelog"))}})]}),S("screenshots")&&Object.keys(S("screenshots")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Screenshots"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(S("screenshots")).slice(0,6).map(([x,N])=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("img",{src:N.src,alt:N.caption||`Screenshot ${x}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200",loading:"lazy"}),N.caption&&e.jsx("p",{className:"text-sm text-gray-600",children:N.caption})]},x))})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plugin Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("version")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Rank"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",t.currentRank||"N/A"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:T(S("last_updated"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Added"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:T(S("added"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Requires WP"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("requires")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Tested up to"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("tested")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"PHP Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("requires_php")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Active Installs"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("active_installs")?`${S("active_installs").toLocaleString()}+`:"N/A"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Download"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 flex gap-1 items-center",children:["Current Version",e.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Latest"})]}),e.jsxs("div",{className:"text-md font-bold text-gray-900",children:["v",S("version")||"N/A"]}),e.jsx("button",{onClick:()=>window.open(S("download_link"),"_blank"),className:"bg-green-600 hover:bg-green-700 text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:e.jsx(xe,{className:"h-4 w-4"})})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Older Versions"}),h?e.jsx("div",{className:"border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-500",children:"Loading versions..."}):e.jsxs("select",{value:k,onChange:x=>w(x.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select a version..."}),Object.entries(p).filter(([x])=>{var N;return x!==((N=t==null?void 0:t.pluginData)==null?void 0:N.version)}).sort((x,N)=>{const R=H=>H.split(".").map(Number),[_,U]=[R(x[0]),R(N[0])];for(let H=0;H<Math.max(_.length,U.length);H++){const z=(U[H]||0)-(_[H]||0);if(z!==0)return z}return 0}).slice(0,15).map(([x])=>e.jsxs("option",{value:x,children:["v",x]},x))]}),e.jsx("button",{onClick:x=>{x.preventDefault(),k&&(p!=null&&p[k])&&(D(!0),window.open(p[k],"_blank"),setTimeout(()=>D(!1),2e3))},disabled:!k||y||h,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:y?e.jsx(e.Fragment,{children:e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"})}):e.jsx(e.Fragment,{children:e.jsx(xe,{className:"h-4 w-4"})})})]})})]}),S("donate_link")&&e.jsxs("button",{onClick:()=>window.open(S("donate_link"),"_blank"),className:"w-full bg-red-100 hover:bg-red-200 text-red-700 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[e.jsx(Ys,{className:"h-4 w-4"}),e.jsx("span",{children:"Donate"})]})]}),S("author")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Author"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Pe,{className:"h-8 w-8 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:S("author").replace(/<[^>]*>/g,"")}),S("author_profile")&&e.jsx("a",{href:S("author_profile"),target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800",children:"View Profile"})]})]})]}),S("tags")&&Object.keys(S("tags")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.keys(S("tags")).slice(0,10).map(x=>e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:x},x))})]}),S("contributors")&&Object.keys(S("contributors")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Contributors"}),e.jsx("div",{className:"flex flex-wrap gap-4",children:Object.entries(S("contributors")).slice(0,10).map(([x,N])=>e.jsx("a",{href:N.profile,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 rounded-lg shadow-sm",children:e.jsx("img",{src:N.avatar,alt:N.display_name||x,title:N.display_name||x,className:"w-8 h-8 rounded-full"})},x))})]})]})]})})]})};function Tt(){return e.jsx(it,{children:e.jsx(lt,{children:e.jsx(As,{children:e.jsxs(Ps,{children:[e.jsx(ce,{path:"/login",element:e.jsx(ht,{})}),e.jsxs(ce,{path:"/*",element:e.jsx(ct,{children:e.jsx(xt,{})}),children:[e.jsx(ce,{path:"dashboard",element:e.jsx(yt,{})})," ",e.jsx(ce,{path:"plugin-rank",element:e.jsx(bt,{})}),e.jsx(ce,{path:"keyword-analysis",element:e.jsx(wt,{})}),e.jsx(ce,{path:"analytics",element:e.jsx(Nt,{})}),e.jsx(ce,{path:"users",element:e.jsx(St,{})}),e.jsx(ce,{path:"settings",element:e.jsx(At,{})}),e.jsx(ce,{path:"profile",element:e.jsx(Pt,{})}),e.jsx(ce,{path:"plugin-details/:slug",element:e.jsx($t,{})}),e.jsx(ce,{path:"",element:e.jsx(ls,{to:"/dashboard",replace:!0})})]})]})})})})}ps(document.getElementById("root")).render(e.jsx(a.StrictMode,{children:e.jsx(Tt,{})}));
